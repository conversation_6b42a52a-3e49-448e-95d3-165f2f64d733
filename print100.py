"""
تقارير PDF للأقسام مع دعم كامل للغة العربية وخطوط واضحة
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

try:
    from fpdf import FPDF
    from fpdf.enums import XPos, YPos
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    print("المكتبات المطلوبة غير متوفرة. جاري تثبيتها...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
        from fpdf import FPDF
        from fpdf.enums import XPos, YPos
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("تم تثبيت المكتبات بنجاح!")
    except Exception as e:
        print(f"فشل تثبيت المكتبات: {e}")
        sys.exit(1)

# استيراد الفئات والدوال المطلوبة من print10.py
from print10 import ArabicPDF, download_arabic_fonts, reshape_ar

def generate_section_pdf_report(level_name, section_name, exams_data, pdf_filename, current_year, school_name_str, dark_blue_color_hex, current_semester=None):
    """
    إنشاء تقرير PDF لفروض القسم باستخدام FPDF.

    :param level_name: اسم المستوى.
    :param section_name: اسم القسم.
    :param exams_data: قائمة بالفروض (tuples من جدول مسك_أوراق_الفروض).
    :param pdf_filename: المسار الكامل لحفظ ملف PDF.
    :param current_year: السنة الدراسية الحالية.
    :param school_name_str: اسم المؤسسة.
    :param dark_blue_color_hex: لون أزرق داكن (لا يستخدم في FPDF).
    :param current_semester: الأسدس المحدد (اختياري).
    :return: True إذا تم إنشاء التقرير بنجاح، False خلاف ذلك.
    """
    # التأكد من توفر الخطوط العربية
    download_arabic_fonts()

    # التحقق من وجود ملف الخط الأساسي
    arial_regular_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "arial.ttf")
    if not os.path.exists(arial_regular_path):
        print(f"خطأ: ملف الخط الأساسي 'arial.ttf' مفقود في مجلد 'fonts'. لا يمكن إنشاء التقرير.")
        return False

    try:
        # إنشاء PDF
        pdf = ArabicPDF()
        if not pdf.arial_regular_available:
            print("فشل تهيئة ArabicPDF بسبب عدم توفر الخط الأساسي.")
            return False

        # --- الحصول على السنة الدراسية والأسدس الحاليين من بيانات المؤسسة ---
        current_academic_year = None
        
        # استخدام الأسدس المرسل كمعامل أو الجلب من قاعدة البيانات
        if current_semester:
            # استخدام الأسدس المرسل
            semester_to_use = current_semester
        else:
            # جلب الأسدس من قاعدة البيانات
            semester_to_use = None
        
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                current_academic_year = result[0]
                if not semester_to_use:  # إذا لم يتم تمرير أسدس، استخدم الأسدس من قاعدة البيانات
                    semester_to_use = result[1]
            conn.close()
        except Exception as e:
            print(f"خطأ في جلب السنة الدراسية والأسدس من بيانات المؤسسة: {e}")
            # في حالة الخطأ، استخدام القيم الافتراضية
            current_academic_year = current_year
            if not semester_to_use:
                semester_to_use = "الأسدس الأول"

        # Calcular número de registros al principio
        num_records = len(exams_data)
        if num_records == 0:
            print("لا توجد بيانات فروض لعرضها في التقرير")
            return False

        # --- تحسين إضافة الشعار باستخدام تقنية print5.py ---
        logo_path = None
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            
            # استعلام مشابه لـ print5.py للحصول على مسار الشعار
            cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                if result[0] and os.path.exists(result[0]):
                    logo_path = result[0]
                # استخدام اسم المؤسسة من قاعدة البيانات إذا كان متوفرًا
                if result[1] and result[1].strip():
                    school_name_str = result[1]
            
            conn.close()
        except Exception as e:
            print(f"خطأ في البحث عن شعار المؤسسة: {e}")

        # إذا لم يتم العثور على الشعار في قاعدة البيانات، نبحث في المجلد المحلي
        if not logo_path:
            # البحث عن ملفات الشعار المحتملة
            possible_logo_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "logo.png"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "images", "logo.png"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "logo.png")
            ]
            
            for path in possible_logo_paths:
                if os.path.exists(path):
                    logo_path = path
                    print(f"تم العثور على الشعار في: {logo_path}")
                    break
        
        # --- إنشاء الترويسة ---
        # مواصفات مماثلة لـ print5.py: عرض 200 وارتفاع 90
        y_position = 10  # الموضع العمودي الأولي
        
        # إضافة الشعار إن وجد في وسط الصفحة مع ضبط الأبعاد (كما في print5.py)
        if logo_path:
            try:
                # استخدام نفس أبعاد الشعار من print5.py: عرض 200 ارتفاع 90 نقطة
                # تحويل من نقاط إلى ملم (1 نقطة = 0.3528 ملم تقريباً)
                logo_width = 200 * 0.3528
                logo_height = 90 * 0.3528
                
                # حساب موقع X للتوسيط
                page_width = 210  # عرض A4 بالملم
                logo_x = (page_width - logo_width) / 2
                
                pdf.image(logo_path, x=logo_x, y=y_position, w=logo_width, h=logo_height)
                print(f"تم إضافة الشعار بنجاح من: {logo_path}")
                
                # تحديث موضع Y بعد إضافة الشعار مع مسافة مقللة
                y_position += logo_height + 2
            except Exception as logo_err:
                print(f"خطأ في تحميل الشعار: {logo_err}")
                traceback.print_exc()
                y_position = 10  # إعادة تعيين موضع Y في حالة الخطأ
        
        # كتابة اسم المؤسسة بالتنسيق المناسب في وسط الصفحة
        school_name_font_size = 17  # حجم الخط المستخدم في print5.py
        
        # حساب موضع X لتوسيط النص
        pdf.set_font('Arial', 'B', school_name_font_size)
        school_name_reshaped = reshape_ar(school_name_str)
        text_width = pdf.get_string_width(school_name_reshaped)
        page_width = 210  # عرض A4 بالملم
        center_x = (page_width - text_width) / 2
        
        pdf.set_xy(center_x, y_position)
        pdf.cell(text_width, 10, school_name_reshaped, align='C')
        y_position += 8
        
        # إضافة السنة الدراسية والأسدس في وسط الصفحة
        year_semester_text = f"السنة الدراسية: {current_academic_year} - الأسدس: {semester_to_use}"
        pdf.set_font('Arial', '', 14)
        year_semester_reshaped = reshape_ar(year_semester_text)
        year_semester_width = pdf.get_string_width(year_semester_reshaped)
        center_x_year = (page_width - year_semester_width) / 2
        
        pdf.set_xy(center_x_year, y_position)
        pdf.cell(year_semester_width, 10, year_semester_reshaped, align='C')
        y_position += 8
        
        # إضافة عنوان التقرير في وسط الصفحة
        title = f"تقرير الفروض الممسوكة للقسم: {section_name} - المستوى: {level_name}"
        
        # حساب موضع X لتوسيط عنوان التقرير
        pdf.set_font('Arial', 'B', 14)
        title_reshaped = reshape_ar(title)
        title_width = pdf.get_string_width(title_reshaped)
        center_x_title = (page_width - title_width) / 2
        
        pdf.set_xy(center_x_title, y_position)
        pdf.cell(title_width, 10, title_reshaped, align='C')
        y_position += 10

        # --- جدول الفروض الرئيسي (بدون عمود المستوى لأنه مذكور في العنوان) ---
        # عناوين الأعمدة الجديدة (بدون المستوى)
        headers = [reshape_ar("المادة"), reshape_ar("الأستاذ"), reshape_ar("نوع الفرض"), reshape_ar("تاريخ الإنجاز"), reshape_ar("الأسدس")]
        
        # تحسين عرض الأعمدة بحسب المحتوى الجديد
        col_widths = [30, 40, 30, 30, 30]  # عرض العمود بالملم
        
        # إعداد مواصفات الجدول
        table_x = (210 - sum(col_widths)) / 2  # توسيط الجدول في الصفحة
        table_y = y_position
        cell_height = 7  # ارتفاع الخلية 8 نقطة بدلاً من 10
        
        # رسم خلفية رأس الجدول (أزرق غامق)
        pdf.set_fill_color(0, 51, 102)  # RGB للون أزرق غامق
        pdf.rect(table_x, table_y, sum(col_widths), cell_height, style='F')
        
        # كتابة عناوين الأعمدة بلون أبيض وخط Calibri 11 غامق
        pdf.set_text_color(255, 255, 255)  # لون أبيض
        current_x = table_x
        for i, header in enumerate(headers):
            pdf.set_xy(current_x, table_y)
            # استخدام Calibri إذا كان متوفرًا، وإلا Arial
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)  # خط Calibri غامق، حجم 14
            else:
                pdf.set_font('Arial', 'B', 13)  # بديل: خط Arial غامق، حجم 14
                
            pdf.cell(col_widths[i], cell_height, header, border=0, align='C')
            current_x += col_widths[i]
        
        # الانتقال للصف التالي والبدء في إضافة البيانات
        table_y += cell_height
        pdf.set_text_color(0, 0, 0)  # العودة للون الأسود للنص
        
        # إضافة بيانات الفروض مع تحسينات في التنسيق (باستخدام جميع الأعمدة الخمسة)
        for i, exam in enumerate(exams_data):
            # تلوين خلفية الصف بالتناوب
            if i % 2 == 1:  # الصفوف الفردية
                pdf.set_fill_color(240, 240, 240)  # لون رمادي فاتح جداً
                pdf.rect(table_x, table_y, sum(col_widths), cell_height, style='F')
            
            # كتابة بيانات الصف (جميع الأعمدة الخمسة)
            current_x = table_x
            for j, col_index in enumerate(range(5)):  # المؤشرات للأعمدة الخمسة
                pdf.set_xy(current_x, table_y)
                
                # استخدام Calibri 13 غامق للصفوف
                if pdf.calibri_bold_available:
                    pdf.set_font('Calibri', 'B', 11)  # خط Calibri غامق، حجم 13
                else:
                    pdf.set_font('Arial', 'B', 11)  # بديل: خط Arial غامق، حجم 13
                
                # تشكيل النص العربي
                reshaped_text = reshape_ar(str(exam[col_index]))
                
                # كتابة البيانات في الخلية
                pdf.cell(col_widths[j], cell_height, reshaped_text, border=0, align='C')
                current_x += col_widths[j]
            
            # الانتقال للصف التالي
            table_y += cell_height
        
        # رسم إطار خارجي للجدول كامل
        pdf.set_draw_color(0, 0, 0)  # لون أسود للإطار الخارجي
        pdf.rect(table_x, y_position, sum(col_widths), cell_height * (len(exams_data) + 1), 'D')
        
        # رسم خط أفقي بعد رأس الجدول
        pdf.set_line_width(0.5)  # زيادة سمك الخط
        pdf.line(table_x, y_position + cell_height, table_x + sum(col_widths), y_position + cell_height)
        pdf.set_line_width(0.3)  # إعادة سمك الخط للقيمة الافتراضية

        # --- إضافة الإحصائيات ---
        summary_y = table_y + 10
        pdf.write_arabic(10, summary_y, f"إجمالي عدد الفروض: {len(exams_data)}", size=10, bold=True)
        summary_y += 8
        pdf.write_arabic(10, summary_y, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d')}", size=8)
        
        # --- إضافة حقول التوقيعات مثل print10.py ---
        signatures_y = summary_y + 20  # مسافة كافية بعد الإحصائيات
        
        # توقيع الحارس(ة) العام(ة) - في الوسط
        guard_signature_text = "توقيع الحارس(ة) العام(ة):"
        guard_signature_reshaped = reshape_ar(guard_signature_text)
        pdf.set_font('Arial', 'B', 12)
        
        # موضع توقيع الحارس - في وسط الصفحة
        guard_signature_width = pdf.get_string_width(guard_signature_reshaped)
        guard_x_position = (page_width - guard_signature_width) / 2
        pdf.set_xy(guard_x_position, signatures_y)
        pdf.cell(guard_signature_width, 10, guard_signature_reshaped, align='C')
        
        # رسم خط للتوقيع تحت النص
        pdf.line(guard_x_position, signatures_y + 15, guard_x_position + guard_signature_width, signatures_y + 15)

        # حفظ الملف
        pdf.output(pdf_filename)
        return True

    except Exception as e:
        print(f"خطأ في إنشاء تقرير القسم: {e}")
        traceback.print_exc()
        return False
