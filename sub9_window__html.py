import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, QObject, pyqtSignal, pyqtSlot
from PyQt5.QtSql import QSqlQuery
import sqlite3
import json

class WebBridge(QObject):
    """فئة الجسر بين JavaScript و Python"""
    
    # إشارات للتفاعل مع النافذة الرئيسية
    dataRequested = pyqtSignal()
    listsDataRequested = pyqtSignal(str, str)  # code, name
    searchRequested = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        
    @pyqtSlot()
    def refreshData(self):
        """طلب تحديث البيانات"""
        try:
            self.dataRequested.emit()
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            self.showMessage("حدث خطأ أثناء تحديث البيانات", "error")
    
    @pyqtSlot(str)
    def searchGeneral(self, search_text):
        """البحث في السجل العام"""
        try:
            self.searchRequested.emit(search_text)
        except Exception as e:
            print(f"خطأ في البحث: {e}")
            self.showMessage("حدث خطأ أثناء البحث", "error")
    
    @pyqtSlot(str, str)
    def loadListsData(self, code, name):
        """تحميل بيانات اللوائح للرمز المحدد"""
        try:
            self.listsDataRequested.emit(code, name)
        except Exception as e:
            print(f"خطأ في تحميل بيانات اللوائح: {e}")
            self.showMessage("حدث خطأ أثناء تحميل بيانات اللوائح", "error")
    
    @pyqtSlot(str)
    def copyCodeToClipboard(self, code):
        """نسخ الرمز إلى الحافظة"""
        try:
            from PyQt5.QtGui import QClipboard
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(code)
            self.showMessage(f"تم نسخ الرمز: {code}", "success")
        except Exception as e:
            print(f"خطأ في نسخ الرمز: {e}")
            self.showMessage("حدث خطأ أثناء نسخ الرمز", "error")
    
    def showMessage(self, message, message_type="info"):
        """عرض رسالة للمستخدم عبر JavaScript فقط"""
        try:
            message_data = json.dumps({"message": message, "type": message_type}, ensure_ascii=False)
            if self.parent_window and hasattr(self.parent_window, 'web_view'):
                self.parent_window.web_view.page().runJavaScript(f"showNotification({message_data});")
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

class GeneralRecordHtmlWindow(QMainWindow):
    """نافذة بحث السجل العام واللوائح باستخدام HTML"""
    
    def __init__(self, parent=None, db=None, academic_year=None):
        super().__init__(parent)
        self.db = db
        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())
        self.current_academic_year = academic_year or "2024/2025"
        
        self.setWindowTitle("نافذة بحث السجل العام واللوائح")
        self.setMinimumSize(1200, 800)
        
        # إعداد أيقونة النافذة
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        self.setup_ui()
        self.setup_bridge()
        self.load_html_content()
        
        # إعداد خاص عند الدمج في النافذة الرئيسية
        if parent is not None:
            self.setup_for_embedding()
        
    def setup_for_embedding(self):
        """إعدادات خاصة عند دمج النافذة في النافذة الرئيسية"""
        try:
            # ضمان ظهور أسهم التمرير
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = self.web_view.settings()
            settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
            settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
            settings.setAttribute(QWebEngineSettings.SpatialNavigationEnabled, True)
            
            # إعدادات إضافية للنافذة
            self.setMinimumSize(1000, 700)
            
            # تطبيق أنماط إضافية عبر JavaScript
            self.web_view.loadFinished.connect(self.apply_embedding_fixes)
            
        except Exception as e:
            print(f"خطأ في إعداد النافذة للدمج: {e}")
    
    def apply_embedding_fixes(self):
        """تطبيق إصلاحات JavaScript للدمج"""
        try:
            js_code = """
            // إجبار ظهور أسهم التمرير
            document.querySelectorAll('.table-wrapper').forEach(function(wrapper) {
                wrapper.style.overflowY = 'scroll';
                wrapper.style.overflowX = 'auto';
                wrapper.style.scrollbarWidth = 'auto';
                wrapper.style.msOverflowStyle = 'auto';
            });
            
            // التأكد من أن الجسم يدعم التمرير
            document.body.style.overflow = 'auto';
            document.documentElement.style.overflow = 'auto';
            
            console.log('تم تطبيق إصلاحات التمرير للدمج');
            """
            
            self.web_view.page().runJavaScript(js_code)
            
        except Exception as e:
            print(f"خطأ في تطبيق إصلاحات JavaScript: {e}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        
        # إعدادات خاصة للتمرير عند الدمج
        from PyQt5.QtWebEngineWidgets import QWebEngineSettings
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
        settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
        
        # إعدادات إضافية لضمان عمل التمرير
        self.web_view.setMinimumSize(800, 600)
        
        layout.addWidget(self.web_view)
        
    def setup_bridge(self):
        """إعداد الجسر بين Python و JavaScript"""
        self.bridge = WebBridge(self)
        
        # ربط الإشارات
        self.bridge.dataRequested.connect(self.load_general_data)
        self.bridge.listsDataRequested.connect(self.handle_legacy_lists_request)
        self.bridge.searchRequested.connect(self.search_general_data)
        
    def load_html_content(self):
        """تحميل محتوى HTML"""
        html_content = self.generate_html()
        self.web_view.setHtml(html_content)
        
        # تحميل البيانات بعد تحميل HTML
        self.web_view.loadFinished.connect(self.on_load_finished)
        
    def on_load_finished(self):
        """يتم استدعاؤها عند انتهاء تحميل الصفحة"""
        # إعداد القناة للتواصل مع JavaScript
        from PyQt5.QtWebChannel import QWebChannel
        
        self.channel = QWebChannel()
        self.channel.registerObject("bridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)
        
        # تطبيق إصلاحات التمرير
        self.apply_scrollbar_fixes()
        
        # تأخير قصير للتأكد من تحميل QWebChannel
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(500, self.load_general_data)
        
    def apply_scrollbar_fixes(self):
        """تطبيق إصلاحات خاصة لأسهم التمرير"""
        try:
            scrollbar_js = """
            // إجبار ظهور أسهم التمرير في جميع العناصر
            function forceScrollbars() {
                // للجداول
                const tableWrappers = document.querySelectorAll('.table-wrapper');
                tableWrappers.forEach(function(wrapper) {
                    wrapper.style.overflow = 'auto';
                    wrapper.style.overflowY = 'scroll';
                    wrapper.style.overflowX = 'auto';
                    
                    // إضافة محتوى وهمي مؤقت للتأكد من ظهور التمرير
                    const originalHeight = wrapper.style.maxHeight;
                    wrapper.style.maxHeight = '200px';
                    
                    // التأكد من أن المحتوى أطول من الحاوية
                    const table = wrapper.querySelector('table');
                    if (table) {
                        const tbody = table.querySelector('tbody');
                        if (tbody && tbody.children.length === 0) {
                            // إضافة صفوف وهمية مؤقتاً
                            for (let i = 0; i < 10; i++) {
                                const row = document.createElement('tr');
                                row.innerHTML = '<td colspan="7" style="height: 30px;">جاري التحميل...</td>';
                                tbody.appendChild(row);
                            }
                            
                            // إزالة الصفوف الوهمية بعد ثانية
                            setTimeout(() => {
                                if (tbody.children.length > 1) {
                                    while (tbody.children.length > 1) {
                                        tbody.removeChild(tbody.lastChild);
                                    }
                                }
                            }, 1000);
                        }
                    }
                });
                
                // للجسم
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                console.log('تم تطبيق إصلاحات أسهم التمرير');
            }
            
            // تطبيق الإصلاحات فوراً
            forceScrollbars();
            
            // إعادة تطبيق الإصلاحات عند تحديث البيانات
            setTimeout(forceScrollbars, 100);
            setTimeout(forceScrollbars, 500);
            setTimeout(forceScrollbars, 1000);
            """
            
            self.web_view.page().runJavaScript(scrollbar_js)
            
        except Exception as e:
            print(f"خطأ في تطبيق إصلاحات أسهم التمرير: {e}")
        
    def generate_html(self):
        """إنشاء محتوى HTML للنافذة"""
        return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة بحث السجل العام واللوائح</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            /* إعدادات التمرير للجسم */
            overflow: auto;
            scrollbar-width: auto;
            -ms-overflow-style: auto;
        }
        
        /* تخصيص أسهم التمرير للجسم */
        body::-webkit-scrollbar {
            width: 14px;
        }
        
        body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            /* إعدادات التمرير للحاوية */
            overflow: visible;
            min-height: 600px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        
        .header h1 {
            color: #0d47a1;
            font-family: 'Calibri', sans-serif;
            font-size: 22pt;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-section {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .search-container {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .search-label {
            color: #0d47a1;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            font-size: 17px;
            min-width: 80px;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 12px 20px;
            border: 2px solid #dce1e6;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 14px;
            font-weight: bold;
            background-color: #f7f9fc;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border: 2px solid #1976d2;
            background-color: white;
            outline: none;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn-clear {
            background: linear-gradient(45deg, #f44336, #e53935);
            color: white;
            min-width: 80px;
        }
        
        .btn-refresh {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }
        
        .results-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
        }
        
        .results-count {
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #0d47a1;
            font-size: 17px;
        }
        
        .table-section {
            margin-bottom: 30px;
        }
        
        .table-title {
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #0d47a1;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #1976d2;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .table-wrapper {
            max-height: 200px;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 10px;
            /* إجبار ظهور أسهم التمرير */
            scrollbar-width: auto; /* Firefox */
            -ms-overflow-style: auto; /* IE and Edge */
        }
        
        /* تخصيص أسهم التمرير للمتصفحات WebKit */
        .table-wrapper::-webkit-scrollbar {
            width: 14px;
            height: 14px;
            background: #f8f9fa;
        }
        
        .table-wrapper::-webkit-scrollbar-track {
            background: #e9ecef;
            border-radius: 7px;
            border: 1px solid #dee2e6;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 7px;
            border: 1px solid #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-corner {
            background: #e9ecef;
        }
        
        /* إجبار ظهور التمرير دائماً */
        .table-wrapper {
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #6c757d #e9ecef; /* Firefox */
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: linear-gradient(45deg, #e3f2fd, #bbdefb);
            color: #0d47a1;
            padding: 15px 10px;
            text-align: center;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            font-size: 17px;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid #1976d2;
        }
        
        .table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1a1a1a;
        }
        
        .table tbody tr {
            transition: background-color 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #e3f2fd;
            cursor: pointer;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .table tbody tr.selected {
            background-color: #bbdefb !important;
            color: #01579b;
        }

        .table tbody tr.processing {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .code-cell {
            color: #3498db;
            font-weight: bold;
            cursor: pointer;
        }
        
        .code-cell:hover {
            color: #2980b9;
            text-decoration: underline;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #7f8c8d;
            animation: pulse 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes pulse {
            from { opacity: 0.6; }
            to { opacity: 1; }
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #95a5a6;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: 'Calibri', sans-serif;
            max-width: 400px;
            direction: rtl;
            text-align: right;
            animation: slideIn 0.3s ease-out;
        }
        
        .notification-success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        
        .notification-warning { 
            background: #fff3cd; 
            color: #856404; 
            border: 1px solid #ffeaa7; 
        }
        
        .notification-error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        
        .notification-info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        
        .notification-content { 
            display: flex; 
            align-items: center; 
            justify-content: space-between; 
        }
        
        .notification-message { 
            flex: 1; 
            margin-right: 10px; 
        }
        
        .notification-close {
            background: none; 
            border: none; 
            font-size: 18px; 
            cursor: pointer;
            color: inherit; 
            opacity: 0.7; 
            padding: 0; 
            width: 20px; 
            height: 20px;
        }
        
        .notification-close:hover { 
            opacity: 1; 
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .search-container {
                flex-direction: column;
            }
            
            .search-input {
                min-width: 100%;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 نافذة بحث السجل العام واللوائح</h1>
            <p>نظام متطور للبحث في بيانات السجل العام واللوائح الدراسية</p>
        </div>
        
        <div class="search-section">
            <div class="search-container">
                <span class="search-label">البحث:</span>
                <input type="text" id="searchInput" class="search-input" 
                       placeholder="اكتب للبحث في السجل العام..." 
                       onkeyup="searchGeneral()">
                <button class="btn btn-clear" onclick="clearSearch()">✕ مسح</button>
                <button class="btn btn-refresh" onclick="refreshData()">🔄 تحديث</button>
            </div>
        </div>
        
        <div class="results-info">
            <span id="resultsCount" class="results-count">النتائج: 0</span>
            <span class="results-count">السنة الدراسية الحالية: 2024/2025</span>
        </div>
        
        <div class="table-section">
            <div class="table-title">📋 السجل العام</div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="table" id="generalTable">
                        <thead>
                            <tr>
                                <th style="width: 80px;">الرمز</th>
                                <th style="width: 200px;">الاسم والنسب</th>
                                <th style="width: 80px;">النوع</th>
                                <th style="width: 120px;">تاريخ الازدياد</th>
                                <th style="width: 150px;">مكان الازدياد</th>
                                <th style="width: 120px;">الهاتف الأول</th>
                                <th style="width: 120px;">الهاتف الثاني</th>
                            </tr>
                        </thead>
                        <tbody id="generalTableBody">
                            <tr>
                                <td colspan="7" class="loading">جاري تحميل البيانات...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="table-section">
            <div class="table-title">📚 اللوائح المرتبطة</div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="table" id="listsTable">
                        <thead>
                            <tr>
                                <th style="width: 120px;">السنة الدراسية</th>
                                <th style="width: 200px;">المستوى</th>
                                <th style="width: 200px;">الاسم والنسب</th>
                                <th style="width: 120px;">القسم</th>
                                <th style="width: 80px;">رت</th>
                            </tr>
                        </thead>
                        <tbody id="listsTableBody">
                            <tr>
                                <td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let bridge;
        let generalData = [];
        let listsData = [];
        let filteredData = [];
        let selectedCode = null;
        let selectedName = null;
        let isProcessing = false; // متغير لمنع النقرات المتعددة
        
        // إعداد القناة مع Python
        function setupBridge() {
            try {
                if (typeof QWebChannel !== 'undefined') {
                    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                        new QWebChannel(qt.webChannelTransport, function(channel) {
                            bridge = channel.objects.bridge;
                            console.log('تم إنشاء الجسر بنجاح');
                        });
                    } else {
                        console.log('qt.webChannelTransport غير متوفر، إعادة المحاولة...');
                        setTimeout(setupBridge, 200);
                    }
                } else {
                    console.log('QWebChannel غير متوفر، إعادة المحاولة...');
                    setTimeout(setupBridge, 200);
                }
            } catch (error) {
                console.log('خطأ في إعداد الجسر:', error);
                setTimeout(setupBridge, 200);
            }
        }
        
        // بدء إعداد الجسر عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupBridge();
        });
        
        // محاولة إضافية بعد تحميل النافذة
        window.addEventListener('load', function() {
            if (!bridge) {
                setTimeout(setupBridge, 300);
            }
            
            // إعادة تطبيق إعدادات التمرير بعد تحميل النافذة
            setTimeout(reapplyScrollbarSettings, 500);
            setTimeout(reapplyScrollbarSettings, 1500);
        });
        
        // تحميل جميع البيانات مرة واحدة
        function initializeData(data) {
            generalData = data.general;
            listsData = data.lists;
            filteredData = generalData;
            updateGeneralTable(generalData);
            
            // إعادة تطبيق إعدادات التمرير بعد التحميل الأولي
            setTimeout(reapplyScrollbarSettings, 200);
        }
        
        // دالة لتنظيف النصوص من الأحرف الخاصة
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // دالة لتنظيف النصوص للاستخدام في JavaScript
        function escapeJs(text) {
            if (!text) return '';
            return text.toString().replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r');
        }

        function updateGeneralTable(data) {
            const tableBody = document.getElementById('generalTableBody');

            if (!data || data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="no-data">📭 لا توجد بيانات للعرض</td></tr>';
                updateResultsCount(0);
                return;
            }

            // مسح الجدول أولاً لتجنب التشنج
            tableBody.innerHTML = '';

            // إنشاء الصفوف باستخدام DOM بدلاً من innerHTML لتجنب مشاكل الأحرف الخاصة
            const fragment = document.createDocumentFragment();

            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.className = 'table-row';

                // إضافة معرف فريد للصف
                tr.setAttribute('data-index', index);
                tr.setAttribute('data-code', row[0] || '');

                // إضافة مستمع الأحداث بطريقة آمنة
                tr.addEventListener('click', function(e) {
                    // تجنب التنفيذ المتكرر
                    if (this.classList.contains('processing')) return;
                    this.classList.add('processing');

                    try {
                        selectGeneralRecord(row[0] || '', row[1] || '', this);
                    } catch (error) {
                        console.error('خطأ في تحديد الصف:', error);
                    } finally {
                        // إزالة علامة المعالجة بعد فترة قصيرة
                        setTimeout(() => {
                            this.classList.remove('processing');
                        }, 100);
                    }
                });

                // إنشاء الخلايا
                const cells = [
                    { content: row[0] || '', className: 'code-cell', isCode: true },
                    { content: row[1] || '', style: 'text-align: right;' },
                    { content: row[2] || '' },
                    { content: row[3] || '' },
                    { content: row[4] || '', style: 'text-align: right;' },
                    { content: row[5] || '' },
                    { content: row[6] || '' }
                ];

                cells.forEach((cellData, cellIndex) => {
                    const td = document.createElement('td');
                    td.textContent = cellData.content;

                    if (cellData.className) {
                        td.className = cellData.className;
                    }

                    if (cellData.style) {
                        td.setAttribute('style', cellData.style);
                    }

                    // إضافة وظيفة النسخ للخلية الأولى (الرمز)
                    if (cellData.isCode && cellData.content) {
                        td.addEventListener('click', function(e) {
                            e.stopPropagation();
                            copyCode(cellData.content);
                        });
                        td.style.cursor = 'copy';
                        td.title = 'انقر لنسخ الرمز';
                    }

                    tr.appendChild(td);
                });

                fragment.appendChild(tr);
            });

            // إضافة جميع الصفوف دفعة واحدة
            tableBody.appendChild(fragment);
            updateResultsCount(data.length);

            // إعادة تطبيق إعدادات التمرير بعد تحديث البيانات
            setTimeout(function() {
                const wrapper = document.querySelector('#generalTable').closest('.table-wrapper');
                if (wrapper) {
                    wrapper.style.overflowY = 'scroll';
                    wrapper.style.overflowX = 'auto';
                }
            }, 50);
        }
        
        function updateListsTable(data) {
            const tableBody = document.getElementById('listsTableBody');

            if (!data || data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="no-data">📭 لا توجد لوائح مرتبطة بهذا الرمز</td></tr>';
                return;
            }

            // مسح الجدول أولاً
            tableBody.innerHTML = '';

            // إنشاء الصفوف باستخدام DOM لتجنب مشاكل الأحرف الخاصة
            const fragment = document.createDocumentFragment();

            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.className = 'lists-row';

                // إنشاء الخلايا
                const cells = [
                    { content: row[0] || '' }, // السنة الدراسية
                    { content: row[1] || '', style: 'text-align: right;' }, // المستوى
                    { content: selectedName || '', style: 'text-align: right;' }, // الاسم
                    { content: row[2] || '', style: 'text-align: right;' }, // القسم
                    { content: row[3] || '' }  // رت
                ];

                cells.forEach((cellData) => {
                    const td = document.createElement('td');
                    td.textContent = cellData.content;

                    if (cellData.style) {
                        td.setAttribute('style', cellData.style);
                    }

                    tr.appendChild(td);
                });

                fragment.appendChild(tr);
            });

            // إضافة جميع الصفوف دفعة واحدة
            tableBody.appendChild(fragment);

            // إعادة تطبيق إعدادات التمرير بعد تحديث بيانات اللوائح
            setTimeout(function() {
                const wrapper = document.querySelector('#listsTable').closest('.table-wrapper');
                if (wrapper) {
                    wrapper.style.overflowY = 'scroll';
                    wrapper.style.overflowX = 'auto';
                }
            }, 50);
        }
        
        function selectGeneralRecord(code, name, rowElement) {
            try {
                // منع النقرات المتعددة السريعة
                if (isProcessing) {
                    return;
                }

                // تجنب التنفيذ المتكرر إذا كان الصف محدد بالفعل
                if (rowElement.classList.contains('selected')) {
                    return;
                }

                // تعيين حالة المعالجة
                isProcessing = true;

                // إزالة التحديد السابق بطريقة محسنة
                const selectedRows = document.querySelectorAll('#generalTableBody tr.selected');
                selectedRows.forEach(row => {
                    row.classList.remove('selected');
                    row.classList.remove('processing');
                });

                // إضافة التحديد الجديد
                rowElement.classList.add('selected');

                selectedCode = code;
                selectedName = name;

                // عرض رسالة تحميل مؤقتة في جدول اللوائح
                const listsTableBody = document.getElementById('listsTableBody');
                listsTableBody.innerHTML = '<tr><td colspan="5" class="loading">جاري تحميل اللوائح المرتبطة...</td></tr>';

                // البحث المحلي في بيانات اللوائح مع تأخير قصير لتجنب التشنج
                setTimeout(() => {
                    try {
                        const relatedLists = listsData.filter(row => row[0] === code);
                        const formattedLists = relatedLists.map(row => [
                            row[1], // السنة الدراسية
                            row[2], // المستوى
                            row[3], // القسم
                            row[4]  // رت
                        ]);

                        updateListsTable(formattedLists);
                    } catch (error) {
                        console.error('خطأ في تحميل اللوائح:', error);
                        listsTableBody.innerHTML = '<tr><td colspan="5" class="no-data">❌ حدث خطأ في تحميل اللوائح</td></tr>';
                    } finally {
                        // إعادة تعيين حالة المعالجة
                        setTimeout(() => {
                            isProcessing = false;
                        }, 100);
                    }
                }, 10);

            } catch (error) {
                console.error('خطأ في تحديد السجل:', error);
                isProcessing = false;
            }
        }
        
        function copyCode(code) {
            if (bridge && typeof bridge.copyCodeToClipboard === 'function') {
                bridge.copyCodeToClipboard(code);
            } else {
                // Fallback للنسخ المباشر
                navigator.clipboard.writeText(code).then(() => {
                    showNotification({message: `تم نسخ الرمز: ${code}`, type: 'success'});
                }).catch(() => {
                    console.log('فشل في نسخ الرمز:', code);
                });
            }
        }
        
        function searchGeneral() {
            const searchText = document.getElementById('searchInput').value.toLowerCase().trim();
            
            if (!searchText) {
                filteredData = generalData;
            } else {
                filteredData = generalData.filter(row => {
                    return row.some(cell => 
                        cell && cell.toString().toLowerCase().includes(searchText)
                    );
                });
            }
            
            updateGeneralTable(filteredData);
            
            // مسح جدول اللوائح عند تغيير البحث
            document.getElementById('listsTableBody').innerHTML = 
                '<tr><td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td></tr>';
        }
        
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            searchGeneral();
        }
        
        function refreshData() {
            document.getElementById('generalTableBody').innerHTML = 
                '<tr><td colspan="7" class="loading">جاري تحديث البيانات...</td></tr>';
            document.getElementById('listsTableBody').innerHTML = 
                '<tr><td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td></tr>';
            
            if (bridge && typeof bridge.refreshData === 'function') {
                bridge.refreshData();
                // إعادة تطبيق إعدادات التمرير بعد التحديث
                setTimeout(reapplyScrollbarSettings, 500);
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
            }
        }
        
        function updateResultsCount(count) {
            document.getElementById('resultsCount').textContent = `النتائج: ${count}`;
        }
        
        // دالة لإعادة تطبيق إعدادات التمرير
        function reapplyScrollbarSettings() {
            try {
                const tableWrappers = document.querySelectorAll('.table-wrapper');
                tableWrappers.forEach(function(wrapper) {
                    wrapper.style.overflowY = 'scroll';
                    wrapper.style.overflowX = 'auto';
                    wrapper.style.scrollbarWidth = 'auto';
                    wrapper.style.msOverflowStyle = 'auto';
                });
                console.log('تم إعادة تطبيق إعدادات التمرير');
            } catch (error) {
                console.error('خطأ في إعادة تطبيق إعدادات التمرير:', error);
            }
        }
        
        // دالة لعرض الإشعارات
        function showNotification(notificationData) {
            try {
                const notification = document.createElement('div');
                notification.className = `notification notification-${notificationData.type || 'info'}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <span class="notification-message">${notificationData.message}</span>
                        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
                
            } catch (error) {
                console.error('خطأ في عرض الإشعار:', error);
                console.log(`إشعار [${notificationData.type}]: ${notificationData.message}`);
            }
        }
    </script>
</body>
</html>
        '''
        
    def load_general_data(self):
        """تحميل جميع البيانات من قاعدة البيانات مرة واحدة"""
        try:
            # تحميل بيانات السجل العام
            general_query_str = """
                SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, 
                       مكان_الازدياد, الهاتف_الأول, الهاتف_الثاني
                FROM السجل_العام
                ORDER BY الرمز
            """
            
            # تحميل جميع بيانات اللوائح
            lists_query_str = """
                SELECT الرمز, السنة_الدراسية, المستوى, القسم, رت
                FROM اللوائح
                ORDER BY الرمز, السنة_الدراسية DESC
            """

            general_records = []
            lists_records = []

            if self.using_qsql:
                # تحميل السجل العام
                query = QSqlQuery(self.db)
                if query.exec_(general_query_str):
                    while query.next():
                        record = [str(query.value(i)) if query.value(i) is not None else '' for i in range(7)]
                        general_records.append(record)
                
                # تحميل اللوائح
                if query.exec_(lists_query_str):
                    while query.next():
                        record = [str(query.value(i)) if query.value(i) is not None else '' for i in range(5)]
                        lists_records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # تحميل السجل العام
                cursor.execute(general_query_str)
                general_records = [[str(value) if value is not None else '' for value in record] for record in cursor.fetchall()]
                
                # تحميل اللوائح
                cursor.execute(lists_query_str)
                lists_records = [[str(value) if value is not None else '' for value in record] for record in cursor.fetchall()]
                
                conn.close()

            # إرسال جميع البيانات إلى JavaScript مرة واحدة
            data_json = json.dumps({
                'general': general_records,
                'lists': lists_records
            }, ensure_ascii=False)
            
            self.web_view.page().runJavaScript(f"initializeData({data_json});")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.bridge.showMessage(f"حدث خطأ أثناء تحميل البيانات: {str(e)}", "error")
    
    def search_general_data(self, search_text):
        """البحث في بيانات السجل العام"""
        # يتم التعامل مع البحث في JavaScript مباشرة للسرعة
        pass
    
    def handle_legacy_lists_request(self, code, name):
        """للتوافق مع النظام القديم - لكن البيانات معالجة محلياً الآن"""
        # لا نحتاج فعل شيء لأن البيانات موجودة محلياً في JavaScript
        pass
    
    def load_lists_data(self, code, name):
        """تحميل بيانات اللوائح للرمز المحدد - لم تعد مستخدمة"""
        # هذه الدالة لم تعد مستخدمة لأن البيانات محملة محلياً
        pass

    def ensure_maximized(self):
        """التأكد من عرض النافذة في كامل الشاشة"""
        self.showMaximized()
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم
    app.setStyle('Fusion')
    
    # إنشاء اتصال بقاعدة البيانات للاختبار
    db_path = os.path.join(os.path.dirname(__file__), "data.db")
    from PyQt5.QtSql import QSqlDatabase
    db = QSqlDatabase.addDatabase("QSQLITE")
    db.setDatabaseName(db_path)
    
    if db.open():
        window = GeneralRecordHtmlWindow(db=db, academic_year="2024/2025")
    else:
        window = GeneralRecordHtmlWindow()
    
    window.show()
    sys.exit(app.exec_())
