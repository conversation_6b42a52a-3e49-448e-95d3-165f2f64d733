"""
تقارير PDF مع دعم كامل للغة العربية وخطوط واضحة
"""

import os
import sys
import sqlite3
import traceback  # Añadir importación de traceback
from datetime import datetime
import subprocess

try:
    from fpdf import FPDF
    from fpdf.enums import XPos, YPos  # Import XPos and YPos
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    print("المكتبات المطلوبة غير متوفرة. جاري تثبيتها...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
        from fpdf import FPDF
        from fpdf.enums import XPos, YPos
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("تم تثبيت المكتبات بنجاح!")
    except Exception as e:
        print(f"فشل تثبيت المكتبات: {e}")
        sys.exit(1)

class ArabicPDF(FPDF):
    """فئة مخصصة لإنشاء ملفات PDF مع دعم اللغة العربية"""
    
    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation, unit, format)
        # تعيين هوامش الصفحة 0.5 سم من جميع الجوانب (5 ملم)
        self.set_margins(5, 5, 5)
        self.add_page()
        # تعيين الهامش السفلي للفاصل التلقائي للصفحات
        self.set_auto_page_break(auto=True, margin=5)
        
        self.fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
        
        if not os.path.exists(self.fonts_dir):
            os.makedirs(self.fonts_dir)
            print(f"تم إنشاء مجلد الخطوط: {self.fonts_dir}")

        # تسجيل الخطوط المطلوبة
        arial_path = os.path.join(self.fonts_dir, "arial.ttf")
        arial_bold_path = os.path.join(self.fonts_dir, "arialbd.ttf")
        calibri_path = os.path.join(self.fonts_dir, "calibri.ttf")
        calibri_bold_path = os.path.join(self.fonts_dir, "calibrib.ttf")

        self.arial_regular_available = False
        self.arial_bold_available = False
        self.calibri_regular_available = False
        self.calibri_bold_available = False

        # محاولة تسجيل Calibri (المفضل)
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_regular_available = True
            print(f"تم تسجيل خط Calibri من {calibri_path}")
        
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
            print(f"تم تسجيل خط Calibri Bold من {calibri_bold_path}")

        # تسجيل Arial كخيار ثاني
        if os.path.exists(arial_path):
            self.add_font('Arial', '', arial_path)
            self.arial_regular_available = True
        else:
            print(f"خطأ فادح: ملف الخط الأساسي {arial_path} غير موجود. لا يمكن متابعة إنشاء PDF بشكل صحيح.")

        if os.path.exists(arial_bold_path):
            self.add_font('Arial', 'B', arial_bold_path)
            self.arial_bold_available = True
        else:
            print(f"تحذير: ملف الخط العريض {arial_bold_path} غير موجود. سيتم استخدام الخط العادي بدلاً من العريض.")
        
        # تعيين الخط الافتراضي (Calibri إذا كان متاحًا، وإلا Arial)
        if self.calibri_regular_available:
            self.set_font('Calibri', '', 12)
        elif self.arial_regular_available:
            self.set_font('Arial', '', 12)
        else:
            print("تحذير: الخطوط العربية غير متوفرة، يتم الرجوع إلى خط أساسي قد لا يدعم العربية بشكل كامل.")
            self.set_font('Helvetica', '', 12)

    def write_arabic(self, x, y, text, font='Calibri', size=12, bold=False):
        """كتابة النصوص العربية في ملف PDF"""
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        self.set_xy(x, y)

        current_style = ''
        if bold and self.calibri_bold_available:
            current_style = 'B'
        elif bold and not self.calibri_bold_available:
            print(f"تنبيه: طلب خط عريض ولكن ملف الخط العريض لـ Calibri غير متوفر. يتم استخدام الخط العادي.")

        if self.calibri_regular_available:
            self.set_font('Calibri', current_style, size)
        else:
            print("تحذير: خط Calibri غير متوفر، يتم الرجوع إلى خط أساسي.")
            self.set_font('Helvetica', current_style, size)

        self.cell(0, 10, bidi_text, new_x=XPos.LMARGIN, new_y=YPos.NEXT, align='R')  # Updated ln=True

def download_arabic_fonts():
    """تنزيل الخطوط العربية إذا لم تكن موجودة"""
    fonts_dir = os.path.join(os.path.dirname(__file__), "fonts")
    if not os.path.exists(fonts_dir):
        os.makedirs(fonts_dir)
    
    # قائمة بجميع ملفات الخطوط المطلوبة
    required_fonts = {
        "arial.ttf": "Arial",
        "arialbd.ttf": "Arial Bold",
        "calibri.ttf": "Calibri", 
        "calibrib.ttf": "Calibri Bold"
    }
    
    missing_fonts = []
    for font_file, font_name in required_fonts.items():
        font_path = os.path.join(fonts_dir, font_file)
        if not os.path.exists(font_path):
            missing_fonts.append(font_file)
    
    if missing_fonts:
        print("الخطوط التالية مفقودة وضرورية للتقرير:")
        for font in missing_fonts:
            print(f" - {font}")
        print(f"يرجى وضع ملفات الخطوط في المجلد: {fonts_dir}")
        
        # محاولة نسخ الخطوط من مجلد خطوط النظام
        try:
            if sys.platform == "win32":
                windows_fonts_dir = os.path.join(os.environ.get('WINDIR', ''), 'Fonts')
                print(f"محاولة نسخ الخطوط من مجلد خطوط النظام: {windows_fonts_dir}")
                for font_file in missing_fonts:
                    windows_font_path = os.path.join(windows_fonts_dir, font_file)
                    if os.path.exists(windows_font_path):
                        import shutil
                        shutil.copy2(windows_font_path, os.path.join(fonts_dir, font_file))
                        print(f"تم نسخ {font_file} من مجلد خطوط النظام")
        except Exception as e:
            print(f"خطأ في محاولة نسخ الخطوط من النظام: {e}")

def reshape_ar(text):
    """إعادة تشكيل النص العربي للعرض الصحيح في PDF"""
    try:
        text = str(text)
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص العربي: {e}")
        return text

def create_certificates_report(records=None):
    """إنشاء تقرير الشهادات المدرسية باستخدام FPDF المحسنة"""
    download_arabic_fonts()
    
    arial_regular_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "arial.ttf")
    if not os.path.exists(arial_regular_path):
        print(f"خطأ: ملف الخط الأساسي 'arial.ttf' مفقود في مجلد 'fonts'. لا يمكن إنشاء التقرير.")
        print(f"يرجى وضع ملف arial.ttf في المجلد: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts')}")
        return None

    pdf = ArabicPDF()
    if not pdf.arial_regular_available:
        print("فشل تهيئة ArabicPDF بسبب عدم توفر الخط الأساسي.")
        return None

    pdf.set_font('Arial', '', 12)
    pdf.write_arabic(10, 10, "تقرير الشهادات المدرسية", bold=True)
    if records:
        for record in records:
            pdf.write_arabic(10, pdf.get_y() + 10, f"الاسم: {record['name']}, الرقم: {record['id']}")
    output_path = os.path.join(os.path.dirname(__file__), "تقرير_الشهادات_المدرسية.pdf")
    pdf.output(output_path)
    print(f"تم إنشاء التقرير بنجاح: {output_path}")
    return output_path

def generate_test_data():
    """توليد بيانات تجريبية للاختبار"""
    return [{"id": i, "name": f"طالب {i}"} for i in range(1, 11)]

def print_certificates_requests(parent=None):
    """طباعة سجلات طلبات الشهادات المدرسية - وظيفة متوافقة مع الدالة الأصلية في print9.py"""
    try:
        reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "طلبات_الشواهد_المدرسية")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(reports_dir, f"طلبات_الشهادات_المدرسية_{current_datetime}.pdf")
        try:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT الرقم, القسم, الرمز, الاسم_والنسب,
                       تاريخ_الطلب, تاريخ_التسليم, ملاحظات
                FROM الشهادة_المدرسية
                ORDER BY id ASC
            """)
            
            records = cursor.fetchall()
            conn.close()
            
            if not records:
                print("لا توجد سجلات للطباعة")
                return False, file_path, reports_dir
        except Exception as e:
            print(f"خطأ في قراءة قاعدة البيانات: {e}")
            return False, "", ""
        
        output_path = create_certificates_report(records)
        if output_path and os.path.exists(output_path):
            print(f"تمت طباعة {len(records)} سجل بنجاح")
            print(f"الملف تم حفظه في: {output_path}")
            
            if parent and hasattr(parent, '__module__') and 'sub19_window' in getattr(parent, '__module__', ''):
                pass
            elif parent:
                try:
                    from print9 import show_print_success_dialog
                    show_print_success_dialog(parent, output_path, reports_dir)
                except ImportError:
                    pass

            return True, output_path, reports_dir
        else:
            print("فشل في إنشاء التقرير")
            return False, "", ""
    except Exception as e:
        print(f"خطأ أثناء طباعة طلبات الشهادات المدرسية: {e}")
        if parent:
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "خطأ", f"حدث خطأ أثناء طباعة طلبات الشهادات المدرسية:\n{str(e)}")
            except ImportError:
                pass
        return False, "", ""

def show_print_success_dialog(parent, output_path, reports_dir):
    """عرض رسالة النجاح بعد طباعة التقرير"""
    try:
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(parent, "نجاح", f"تم إنشاء التقرير بنجاح وحفظه في:\n{output_path}")
    except ImportError:
        pass

def generate_teacher_pdf_report_code(teacher_name, exams_data, pdf_filename, current_year, school_name_str, dark_blue_color_hex):
    """
    إنشاء تقرير PDF لفروض الأستاذ باستخدام FPDF.
    
    :param teacher_name: اسم الأستاذ.
    :param exams_data: قائمة بالفروض (tuples من جدول مسك_أوراق_الفروض).
    :param pdf_filename: المسار الكامل لحفظ ملف PDF.
    :param current_year: السنة الدراسية الحالية.
    :param school_name_str: اسم المؤسسة.
    :param dark_blue_color_hex: لون أزرق داكن (لا يستخدم في FPDF).
    :return: True إذا تم إنشاء التقرير بنجاح، False خلاف ذلك.
    """
    # التأكد من توفر الخطوط العربية
    download_arabic_fonts()
    
    # التحقق من وجود ملف الخط الأساسي
    arial_regular_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "arial.ttf")
    if not os.path.exists(arial_regular_path):
        print(f"خطأ: ملف الخط الأساسي 'arial.ttf' مفقود في مجلد 'fonts'. لا يمكن إنشاء التقرير.")
        return False

    try:
        # إنشاء PDF
        pdf = ArabicPDF()
        if not pdf.arial_regular_available:
            print("فشل تهيئة ArabicPDF بسبب عدم توفر الخط الأساسي.")
            return False

        # --- الحصول على السنة الدراسية والأسدس الحاليين من بيانات المؤسسة ---
        current_academic_year = None
        current_semester = None
        
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                current_academic_year = result[0]
                current_semester = result[1]
            conn.close()
        except Exception as e:
            print(f"خطأ في جلب السنة الدراسية والأسدس من بيانات المؤسسة: {e}")
            # في حالة الخطأ، استخدام القيم الافتراضية
            current_academic_year = current_year
            current_semester = "الأسدس الأول"

        # --- تصفية بيانات الفروض حسب السنة الدراسية والأسدس ---
        # إذا لم يتم تمرير بيانات مفلترة، قم بالتصفية هنا
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT المادة, المستوى, القسم, نوع_الفرض, التاريخ, الأسدس
                FROM مسك_أوراق_الفروض
                WHERE الأستاذ = ? AND السنة_الدراسية = ? AND الأسدس = ?
                ORDER BY التاريخ DESC, المادة, المستوى, القسم, نوع_الفرض
            """, (teacher_name, current_academic_year, current_semester))
            
            filtered_exams_data = cursor.fetchall()
            conn.close()
            
            # استخدام البيانات المفلترة بدلاً من البيانات الأصلية
            if filtered_exams_data:
                exams_data = filtered_exams_data
            else:
                print(f"لا توجد فروض للأستاذ {teacher_name} في السنة الدراسية {current_academic_year} والأسدس {current_semester}")
                return False
                
        except Exception as e:
            print(f"خطأ في تصفية بيانات الفروض: {e}")
            # في حالة الخطأ، استخدام البيانات الأصلية
            pass

        # Calcular número de registros al principio
        num_records = len(exams_data)  # Definir num_records
        if num_records == 0:
            print("لا توجد بيانات فروض لعرضها في التقرير")
            return False

        # --- تحسين إضافة الشعار باستخدام تقنية print5.py ---
        logo_path = None
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            
            # استعلام مشابه لـ print5.py للحصول على مسار الشعار
            cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                if result[0] and os.path.exists(result[0]):
                    logo_path = result[0]
                # استخدام اسم المؤسسة من قاعدة البيانات إذا كان متوفرًا
                if result[1] and result[1].strip():
                    school_name_str = result[1]
            
            conn.close()
        except Exception as e:
            print(f"خطأ في البحث عن شعار المؤسسة: {e}")

        # إذا لم يتم العثور على الشعار في قاعدة البيانات، نبحث في المجلد المحلي
        if not logo_path:
            # البحث عن ملفات الشعار المحتملة
            possible_logo_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "logo.png"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "images", "logo.png"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "logo.png")
            ]
            
            for path in possible_logo_paths:
                if os.path.exists(path):
                    logo_path = path
                    print(f"تم العثور على الشعار في: {logo_path}")
                    break
        
        # --- إنشاء الترويسة ---
        # مواصفات مماثلة لـ print5.py: عرض 200 وارتفاع 90
        y_position = 10  # الموضع العمودي الأولي
        
        # إضافة الشعار إن وجد في وسط الصفحة مع ضبط الأبعاد (كما في print5.py)
        if logo_path:
            try:
                # استخدام نفس أبعاد الشعار من print5.py: عرض 200 ارتفاع 90 نقطة
                # تحويل من نقاط إلى ملم (1 نقطة = 0.3528 ملم تقريباً)
                logo_width = 200 * 0.3528
                logo_height = 90 * 0.3528
                
                # حساب موقع X للتوسيط
                page_width = 210  # عرض A4 بالملم
                logo_x = (page_width - logo_width) / 2
                
                pdf.image(logo_path, x=logo_x, y=y_position, w=logo_width, h=logo_height)
                print(f"تم إضافة الشعار بنجاح من: {logo_path}")
                
                # تحديث موضع Y بعد إضافة الشعار مع مسافة مقللة
                y_position += logo_height + 2
            except Exception as logo_err:
                print(f"خطأ في تحميل الشعار: {logo_err}")
                traceback.print_exc()
                y_position = 10  # إعادة تعيين موضع Y في حالة الخطأ
        
        # كتابة اسم المؤسسة بالتنسيق المناسب في وسط الصفحة
        school_name_font_size = 17  # حجم الخط المستخدم في print5.py
        
        # حساب موضع X لتوسيط النص
        if pdf.calibri_bold_available:
            pdf.set_font('Calibri', 'B', school_name_font_size)
        else:
            pdf.set_font('Calibri', '', school_name_font_size)
        school_name_reshaped = reshape_ar(school_name_str)
        text_width = pdf.get_string_width(school_name_reshaped)
        page_width = 210  # عرض A4 بالملم
        center_x = (page_width - text_width) / 2
        
        pdf.set_xy(center_x, y_position)
        pdf.cell(text_width, 10, school_name_reshaped, align='C')
        y_position += 8
        
        # إضافة السنة الدراسية والأسدس في وسط الصفحة
        year_semester_text = f"السنة الدراسية: {current_academic_year} - الأسدس: {current_semester}"
        if pdf.calibri_regular_available:
            pdf.set_font('Calibri', '', 14)
        else:
            pdf.set_font('Calibri', '', 14)
        year_semester_reshaped = reshape_ar(year_semester_text)
        year_semester_width = pdf.get_string_width(year_semester_reshaped)
        center_x_year = (page_width - year_semester_width) / 2
        
        pdf.set_xy(center_x_year, y_position)
        pdf.cell(year_semester_width, 10, year_semester_reshaped, align='C')
        y_position += 8
        
        # إضافة عنوان التقرير مع المادة في وسط الصفحة
        if exams_data and len(exams_data) > 0:
            subject_name = exams_data[0][0]  # المادة من أول سجل
            title = f"تقرير الفروض المنجزة من طرف الأستاذ(ة): {teacher_name} - مادة: {subject_name}"
        else:
            title = f"تقرير الفروض المنجزة من طرف الأستاذ(ة): {teacher_name}"
        
        # حساب موضع X لتوسيط عنوان التقرير
        if pdf.calibri_bold_available:
            pdf.set_font('Calibri', 'B', 14)
        else:
            pdf.set_font('Calibri', '', 14)
        title_reshaped = reshape_ar(title)
        title_width = pdf.get_string_width(title_reshaped)
        center_x_title = (page_width - title_width) / 2
        
        pdf.set_xy(center_x_title, y_position)
        pdf.cell(title_width, 10, title_reshaped, align='C')
        y_position += 10
        
        # --- جدول الفروض الرئيسي (بدون عمود المادة والمستوى) ---
        # عناوين الأعمدة الجديدة (بدون المادة والمستوى)
        headers = [reshape_ar("القسم"), reshape_ar("نوع الفرض"), reshape_ar("تاريخ الإنجاز"), reshape_ar("الأسدس")]
        
        # تحسين عرض الأعمدة بحسب المحتوى الجديد - بدون عمود المستوى
        col_widths = [30, 40, 30, 30]  # عرض العمود بالملم
        
        # إعداد مواصفات الجدول
        table_x = (210 - sum(col_widths)) / 2  # توسيط الجدول في الصفحة
        table_y = y_position
        cell_height = 7  # ارتفاع الخلية 8 نقطة بدلاً من 10
        
        # رسم خلفية رأس الجدول (أزرق غامق)
        pdf.set_fill_color(0, 51, 102)  # RGB للون أزرق غامق
        pdf.rect(table_x, table_y, sum(col_widths), cell_height, style='F')
        
        # كتابة عناوين الأعمدة بلون أبيض وخط Calibri 11 غامق
        pdf.set_text_color(255, 255, 255)  # لون أبيض
        current_x = table_x
        for i, header in enumerate(headers):
            pdf.set_xy(current_x, table_y)
            # استخدام Calibri فقط
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)  # خط Calibri غامق، حجم 13
            else:
                pdf.set_font('Calibri', '', 13)  # خط Calibri عادي، حجم 13
                
            pdf.cell(col_widths[i], cell_height, header, border=0, align='C')
            current_x += col_widths[i]
        
        # الانتقال للصف التالي والبدء في إضافة البيانات
        table_y += cell_height
        pdf.set_text_color(0, 0, 0)  # العودة للون الأسود للنص
        
        # إضافة بيانات الفروض مع تحسينات في التنسيق (بدون عمود المادة والمستوى)
        for i, exam in enumerate(exams_data):
            # تلوين خلفية الصف بالتناوب
            if i % 2 == 1:  # الصفوف الفردية
                pdf.set_fill_color(240, 240, 240)  # لون رمادي فاتح جداً
                pdf.rect(table_x, table_y, sum(col_widths), cell_height, style='F')
            
            # كتابة بيانات الصف (بدون عمود المادة والمستوى)
            current_x = table_x
            for j, col_index in enumerate([2, 3, 4, 5]):  # المؤشرات المتبقية بعد حذف عمود المادة والمستوى
                pdf.set_xy(current_x, table_y)
                
                # استخدام Calibri فقط للصفوف
                if pdf.calibri_bold_available:
                    pdf.set_font('Calibri', 'B', 11)  # خط Calibri غامق، حجم 11
                else:
                    pdf.set_font('Calibri', '', 11)  # خط Calibri عادي، حجم 11
                
                # تشكيل النص العربي
                reshaped_text = reshape_ar(str(exam[col_index]))
                
                # كتابة البيانات في الخلية
                pdf.cell(col_widths[j], cell_height, reshaped_text, border=0, align='C')
                current_x += col_widths[j]
            
            # الانتقال للصف التالي
            table_y += cell_height
        
        # رسم إطار خارجي للجدول كامل
        pdf.set_draw_color(0, 0, 0)  # لون أسود للإطار الخارجي
        pdf.rect(table_x, y_position, sum(col_widths), cell_height * (len(exams_data) + 1), 'D')
        
        # رسم خط أفقي بعد رأس الجدول
        pdf.set_line_width(0.5)  # زيادة سمك الخط
        pdf.line(table_x, y_position + cell_height, table_x + sum(col_widths), y_position + cell_height)
        pdf.set_line_width(0.3)  # إعادة سمك الخط للقيمة الافتراضية

        # --- إضافة الإحصائيات مع عدد الأقسام الممسوكة ---
        # حساب عدد الأقسام الفريدة
        unique_sections = set()
        for exam in exams_data:
            unique_sections.add(exam[2])  # القسم هو في المؤشر 2
        
        summary_y = table_y + 10
        pdf.write_arabic(13, summary_y, f"إجمالي عدد الفروض: {len(exams_data)}", size=10, bold=True)
        summary_y += 8
        pdf.write_arabic(113, summary_y, f"عدد الأقسام الممسوكة: {len(unique_sections)}", size=10, bold=True)
        summary_y += 8
        pdf.write_arabic(13, summary_y, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d')}", size=8)
        
        # --- إضافة حقول التوقيعات ---
        signatures_y = summary_y + 20  # مسافة كافية بعد الإحصائيات
        
        # توقيع الحارس(ة) العام(ة) - على اليسار
        guard_signature_text = "توقيع الحارس(ة) العام(ة):"
        guard_signature_reshaped = reshape_ar(guard_signature_text)
        if pdf.calibri_bold_available:
            pdf.set_font('Calibri', 'B', 13)
        else:
            pdf.set_font('Calibri', 'B', 13)
        
        # موضع توقيع الحارس - على اليسار
        guard_x_position = 20
        pdf.set_xy(guard_x_position, signatures_y)
        pdf.cell(80, 10, guard_signature_reshaped, align='R')
        
        # رسم خط للتوقيع تحت النص
        pdf.line(guard_x_position, signatures_y + 15, guard_x_position + 70, signatures_y + 15)
        
        # توقيع الأستاذ(ة) - على اليمين
        teacher_signature_text = "توقيع الأستاذ(ة):"
        teacher_signature_reshaped = reshape_ar(teacher_signature_text)
        
        # موضع توقيع الأستاذ - على اليمين
        teacher_x_position = page_width - 90  # من اليمين
        pdf.set_xy(teacher_x_position, signatures_y)
        pdf.cell(80, 10, teacher_signature_reshaped, align='R')
        
        # رسم خط للتوقيع تحت النص
        pdf.line(teacher_x_position, signatures_y + 15, teacher_x_position + 70, signatures_y + 15)

        # حفظ الملف
        pdf.output(pdf_filename)
        return True
    
    except Exception as e:
        print(f"خطأ في إنشاء تقرير الأستاذ: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("===== إنشاء تقرير الشهادات المدرسية باستخدام FPDF المحسنة =====")
    create_certificates_report()

    print("===== اختبار تقارير PDF العربية =====")
    
    # اختبار تقرير الفروض
    mock_teacher_name = "أستاذ تجريبي"
    mock_exams_data = [
        ("الرياضيات", "الأولى إعدادي", "1/1", "الفرض 1", "2024-01-15", "الأسدس الأول"),
        ("الفيزياء", "الثانية إعدادي", "2/3", "الفرض 2", "2024-02-20", "الأسدس الأول"),
    ]
    mock_pdf_filename = "تقرير_الفروض.pdf"
    mock_current_year = "2023/2024"
    mock_school_name = "الثانوية الإعدادية العقاد"
    mock_dark_blue = "#003366"
    
    success = generate_teacher_pdf_report_code(
        mock_teacher_name, 
        mock_exams_data, 
        mock_pdf_filename, 
        mock_current_year, 
        mock_school_name, 
        mock_dark_blue
    )
    
    if success:
        print(f"تم إنشاء تقرير الفروض بنجاح: {mock_pdf_filename}")
        # محاولة فتح الملف
        if sys.platform == "win32":
            os.startfile(mock_pdf_filename)
    else:
        print("فشل إنشاء التقرير")
