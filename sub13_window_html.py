#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
import print3

class ViolationsViewerWindow(QMainWindow):
    """نافذة عرض المخالفات - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # المتغيرات الأساسية
        self.parent_window = parent
        self.violations_data = []
        self.filtered_data = []
        
        # إعداد النافذة
        self.setupUI()

        # تحميل البيانات
        self.load_violations_data()

        # التأكد من فتح النافذة في كامل الشاشة
        self.showMaximized()

    def showEvent(self, event):
        """التأكد من فتح النافذة في كامل الشاشة عند عرضها"""
        super().showEvent(event)
        self.showMaximized()

    def resizeEvent(self, event):
        """منع تصغير النافذة والحفاظ على كامل الشاشة"""
        super().resizeEvent(event)
        # يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle("📊 سجل المخالفات المدرسية - عرض  ")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)
        
        # شريط البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        toolbar_layout.addWidget(search_label)
        
        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("ابحث في جميع الحقول...")
        self.search_entry.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #0d47a1;
                box-shadow: 0 0 5px rgba(13, 71, 161, 0.5);
            }
        """)
        self.search_entry.textChanged.connect(self.on_search)
        toolbar_layout.addWidget(self.search_entry)
        
        # فلتر القسم (بدلاً من المستوى)
        section_label = QLabel("📚 القسم:")
        section_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        toolbar_layout.addWidget(section_label)
        
        self.section_combo = QComboBox()
        self.section_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #0d47a1;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        self.section_combo.currentTextChanged.connect(self.on_filter)
        toolbar_layout.addWidget(self.section_combo)
        
        # فلتر الأستاذ (بدلاً من المادة)
        teacher_label = QLabel("👨‍🏫 الأستاذ:")
        teacher_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        toolbar_layout.addWidget(teacher_label)
        
        self.teacher_combo = QComboBox()
        self.teacher_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #0d47a1;
            }
        """)
        self.teacher_combo.currentTextChanged.connect(self.on_filter)
        toolbar_layout.addWidget(self.teacher_combo)
        
        toolbar_layout.addStretch()
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #45a049
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5cbf60,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3d8b40,
                    stop: 1 #2e7d32
                );
            }
        """)
        self.refresh_button.clicked.connect(self.load_violations_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الأزرار السفلي
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #37474f,
                    stop: 1 #263238
                );
                border-radius: 10px;
                padding: 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 10, 15, 10)
        buttons_layout.setSpacing(15)
        
        # زر طباعة المخالفة المحددة
        self.print_selected_button = QPushButton("🖨️ طباعة المحددة")
        self.print_selected_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_selected_button.setMinimumHeight(40)
        self.print_selected_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #388E3C
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #66BB6A,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2E7D32,
                    stop: 1 #1B5E20
                );
            }
        """)
        self.print_selected_button.clicked.connect(self.print_selected_violation)
        buttons_layout.addWidget(self.print_selected_button)

        # زر حذف المخالفات المحددة
        self.delete_button = QPushButton("🗑️ حذف المحددة")
        self.delete_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_violations)
        buttons_layout.addWidget(self.delete_button)
        
        # زر طباعة سجل المخالفات
        self.print_record_button = QPushButton("📋 طباعة سجل المخالفات")
        self.print_record_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_record_button.setMinimumHeight(40)
        self.print_record_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3,
                    stop: 1 #1976D2
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #42A5F5,
                    stop: 1 #2196F3
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1565C0,
                    stop: 1 #0D47A1
                );
            }
        """)
        self.print_record_button.clicked.connect(self.print_student_violations_record)
        buttons_layout.addWidget(self.print_record_button)
        
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_violations_data(self):
        """تحميل بيانات المخالفات من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل البيانات...")
            
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # تحميل جميع المخالفات (إزالة تاريخ_التسجيل وإجراءات_الحراسة من الاستعلام)
            cursor.execute('''
                SELECT id, التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم,
                       المادة, الأستاذ, الملاحظات, الإجراءات,
                       السنة_الدراسية, الأسدس, رت
                FROM المخالفات ORDER BY التاريخ DESC
            ''')
            
            self.violations_data = cursor.fetchall()
            
            # تحميل قائمة الأقسام للتصفية (بدلاً من المستويات)
            cursor.execute('SELECT DISTINCT القسم FROM المخالفات WHERE القسم IS NOT NULL AND القسم != ""')
            sections = [row[0] for row in cursor.fetchall()]
            self.section_combo.clear()
            self.section_combo.addItems(['الكل'] + sorted(sections))
            
            # تحميل قائمة الأساتذة للتصفية (بدلاً من المواد)
            cursor.execute('SELECT DISTINCT الأستاذ FROM المخالفات WHERE الأستاذ IS NOT NULL AND الأستاذ != ""')
            teachers = [row[0] for row in cursor.fetchall()]
            self.teacher_combo.clear()
            self.teacher_combo.addItems(['الكل'] + sorted(teachers))
            
            conn.close()
            
            # تطبيق الفلاتر وإنشاء HTML
            self.apply_filters()
            
            self.status_bar.showMessage(f"تم تحميل {len(self.violations_data)} مخالفة")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        search_term = self.search_entry.text().lower()
        section_filter = self.section_combo.currentText()
        teacher_filter = self.teacher_combo.currentText()
        
        self.filtered_data = []
        for row in self.violations_data:
            # تطبيق فلتر القسم (الفهرس 5)
            if section_filter != 'الكل' and str(row[5]) != section_filter:
                continue
                
            # تطبيق فلتر الأستاذ (الفهرس 7)
            if teacher_filter != 'الكل' and str(row[7]) != teacher_filter:
                continue
                
            # تطبيق البحث النصي
            if search_term:
                row_text = ' '.join(str(cell or '') for cell in row).lower()
                if search_term not in row_text:
                    continue
                    
            self.filtered_data.append(row)
        
        # إنشاء وعرض HTML
        self.generate_html()
    
    def on_search(self):
        """معالج البحث"""
        self.apply_filters()
    
    def on_filter(self):
        """معالج التصفية"""
        self.apply_filters()
    
    def generate_html(self):
        """توليد HTML لعرض المخالفات"""
        data_to_display = self.filtered_data
        
        html_content = self.create_html_template(data_to_display)
        self.web_view.setHtml(html_content)
    
    def create_html_template(self, data):
        """إنشاء قالب HTML حديث"""
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المخالفات المدرسية</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }}
        
        .table-container {{
            padding: 20px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        th {{
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }}
        
        tr:hover td {{
            background-color: #f8f9fa;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .violation-id {{
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .student-name {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .level {{
            background: #e74c3c;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }}
        
        .subject {{
            background: #27ae60;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }}
        
        .notes {{
            max-width: 200px;
            word-wrap: break-word;
            font-size: 0.9em;
        }}
        
        .record-checkbox {{
            text-align: center;
            width: 60px;
        }}
        
        .record-select {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        #select-all {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        .record-row.selected {{
            background-color: #e3f2fd !important;
            border-right: 4px solid #2196f3;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }}
        
        .empty-icon {{
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }}
        
        @media (max-width: 768px) {{
            .header h1 {{
                font-size: 1.8em;
            }}
            
            table {{
                font-size: 0.8em;
            }}
            
            th, td {{
                padding: 8px;
            }}
        }}
    </style>
    
    <script>
        function toggleSelectAll() {{
            const selectAllBox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.record-select');
            
            checkboxes.forEach(checkbox => {{
                checkbox.checked = selectAllBox.checked;
                updateRowSelection(checkbox);
            }});
        }}
        
        function updateRowSelection(checkbox) {{
            const row = checkbox.closest('tr');
            if (checkbox.checked) {{
                row.classList.add('selected');
            }} else {{
                row.classList.remove('selected');
            }}
        }}
        
        function getSelectedViolations() {{
            const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
            const selectedViolations = [];
            
            selectedCheckboxes.forEach(checkbox => {{
                selectedViolations.push({{
                    id: checkbox.getAttribute('data-id'),
                    student_name: checkbox.getAttribute('data-student-name'),
                    student_code: checkbox.getAttribute('data-student-code'),
                    date: checkbox.getAttribute('data-date')
                }});
            }});
            
            return selectedViolations;
        }}
        
        function getAvailableStudents() {{
            const allCheckboxes = document.querySelectorAll('.record-select');
            const studentsMap = new Map();
            
            allCheckboxes.forEach(checkbox => {{
                const studentCode = checkbox.getAttribute('data-student-code');
                const studentName = checkbox.getAttribute('data-student-name');
                
                if (studentCode && studentName) {{
                    studentsMap.set(studentCode, {{
                        code: studentCode,
                        name: studentName
                    }});
                }}
            }});
            
            return Array.from(studentsMap.values());
        }}
        
        document.addEventListener('DOMContentLoaded', function() {{
            const checkboxes = document.querySelectorAll('.record-select');
            checkboxes.forEach(checkbox => {{
                checkbox.addEventListener('change', function() {{
                    updateRowSelection(this);
                    
                    const allCheckboxes = document.querySelectorAll('.record-select');
                    const checkedBoxes = document.querySelectorAll('.record-select:checked');
                    const selectAllBox = document.getElementById('select-all');
                    
                    if (checkedBoxes.length === 0) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = false;
                    }} else if (checkedBoxes.length === allCheckboxes.length) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = true;
                    }} else {{
                        selectAllBox.indeterminate = true;
                    }}
                }});
            }});
        }});
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 سجل المخالفات المدرسية</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{len(data)}</div>
                <div>إجمالي المخالفات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[3] for row in data if row[3]))}</div>
                <div>عدد التلاميذ</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[5] for row in data if row[5]))}</div>
                <div>الأقسام</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[7] for row in data if row[7]))}</div>
                <div>الأساتذة</div>
            </div>
        </div>
        
        <div class="table-container">
        """
        
        if data:
            html += f"""
            <table>
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
                            اختيار الكل
                        </th>
                        <th>الرقم</th>
                        <th>التاريخ</th>
                        <th>رمز التلميذ</th>
                        <th>اسم التلميذ</th>
                        <th>القسم</th>
                        <th>المادة</th>
                        <th>الأستاذ</th>
                        <th>الملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
            """
            
            for row in data:
                html += f"""
                    <tr class="record-row">
                        <td class="record-checkbox">
                            <input type="checkbox" class="record-select" 
                                   data-id="{row[0] or ''}"
                                   data-student-name="{row[3] or ''}"
                                   data-student-code="{row[2] or ''}"
                                   data-date="{row[1] or ''}">
                        </td>
                        <td><span class="violation-id">{row[0] or 'غير محدد'}</span></td>
                        <td class="date">{row[1] or 'غير محدد'}</td>
                        <td>{row[2] or 'غير محدد'}</td>
                        <td class="student-name">{row[3] or 'غير محدد'}</td>
                        <td>{row[5] or 'غير محدد'}</td>
                        <td><span class="subject">{row[6] or 'غير محدد'}</span></td>
                        <td>{row[7] or 'غير محدد'}</td>
                        <td class="notes">{row[8] or 'غير محدد'}</td>
                        <td>{row[9] or 'غير محدد'}</td>
                    </tr>
                """
            
            html += """
                </tbody>
            </table>
            """
        else:
            html += """
            <div class="empty-state">
                <span class="empty-icon">📋</span>
                <h3>لا توجد مخالفات</h3>
                <p>لم يتم العثور على أي مخالفات تطابق معايير البحث</p>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def delete_selected_violations(self):
        """حذف المخالفات المحددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف المخالفات المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تنفيذ جافا سكريبت للحصول على المخالفات المحددة
            js_code = """
            (function() {
                const selectedViolations = getSelectedViolations();
                return JSON.stringify(selectedViolations);
            })();
            """
            
            def handle_selected_violations(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي مخالفات للحذف!")
                        return
                    
                    selected_violations = json.loads(result)
                    if not selected_violations:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي مخالفات للحذف!")
                        return
                    
                    # حذف المخالفات من قاعدة البيانات
                    conn = sqlite3.connect('data.db')
                    cursor = conn.cursor()
                    deleted_count = 0
                    
                    for violation in selected_violations:
                        cursor.execute(
                            "DELETE FROM المخالفات WHERE id = ?",
                            (violation['id'],)
                        )
                        if cursor.rowcount > 0:
                            deleted_count += 1
                    
                    conn.commit()
                    conn.close()
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} مخالفة بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_violations_data()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} مخالفة")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف المخالفات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة المخالفات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_violations)
            
        except Exception as e:
            print(f"خطأ في حذف المخالفات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_selected_violation(self):
        """طباعة المخالفة المحددة باستخدام print3.py"""
        try:
            # تنفيذ جافا سكريبت للحصول على المخالفة المحددة
            js_code = """
            (function() {
                const selectedViolations = getSelectedViolations();
                return JSON.stringify(selectedViolations);
            })();
            """
            
            def handle_selected_violation(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي مخالفة للطباعة!")
                        return
                    
                    selected_violations = json.loads(result)
                    if not selected_violations:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي مخالفة للطباعة!")
                        return
                    
                    if len(selected_violations) > 1:
                        QMessageBox.information(self, "تنبيه", "يرجى تحديد مخالفة واحدة فقط للطباعة!")
                        return
                    
                    # الحصول على بيانات المخالفة المحددة من قاعدة البيانات
                    violation_id = selected_violations[0]['id']
                    
                    # استخراج بيانات المخالفة الكاملة من قاعدة البيانات
                    conn = sqlite3.connect('data.db')
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT id, التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم,
                               المادة, الأستاذ, الملاحظات, الإجراءات,
                               السنة_الدراسية, الأسدس
                        FROM المخالفات WHERE id = ?
                    ''', (violation_id,))
                    
                    violation_row = cursor.fetchone()
                    conn.close()
                    
                    if not violation_row:
                        QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات المخالفة!")
                        return
                    
                    # تحضير البيانات للطباعة
                    violation_info = {
                        'id': violation_row[0],
                        'date': violation_row[1] or '',
                        'student_code': violation_row[2] or '',
                        'student_name': violation_row[3] or '',
                        'level': violation_row[4] or '',
                        'section': violation_row[5] or '',
                        'subject': violation_row[6] or '',
                        'teacher': violation_row[7] or '',
                        'notes': violation_row[8] or '',
                        'procedures': violation_row[9] or '',
                        'academic_year': violation_row[10] or '',
                        'semester': violation_row[11] or ''
                    }
                    
                    # استيراد وتشغيل دالة الطباعة من print3.py
                    try:
                        success = print3.print_violation_details(violation_info, db_path="data.db")
                        
                        if success:
                            self.status_bar.showMessage("تمت طباعة المخالفة بنجاح ✅")
                            QMessageBox.information(self, "نجح", "تم إنشاء ملف طباعة المخالفة بنجاح! 📄")
                        else:
                            self.status_bar.showMessage("فشلت طباعة المخالفة ❌")
                            QMessageBox.warning(self, "خطأ", "فشل في إنشاء ملف طباعة المخالفة!")
                            
                    except Exception as print_error:
                        print(f"خطأ في طباعة المخالفة: {print_error}")
                        QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة المخالفة:\n{str(print_error)}")
                        
                except Exception as e:
                    print(f"خطأ في معالجة المخالفة المحددة للطباعة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_violation)
            
        except Exception as e:
            print(f"خطأ في طباعة المخالفة المحددة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")

    def print_student_violations_record(self):
        """طباعة سجل مخالفات التلميذ المحدد من الجدول مباشرة"""
        try:
            # تنفيذ جافا سكريبت للحصول على المخالفات المحددة
            js_code = """
            (function() {
                const selectedViolations = getSelectedViolations();
                return JSON.stringify(selectedViolations);
            })();
            """
            
            def handle_selected_violations(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "يرجى تحديد مخالفة واحدة على الأقل من الجدول لطباعة سجل مخالفات التلميذ!")
                        return
                    
                    selected_violations = json.loads(result)
                    if not selected_violations:
                        QMessageBox.information(self, "تنبيه", "يرجى تحديد مخالفة واحدة على الأقل من الجدول لطباعة سجل مخالفات التلميذ!")
                        return
                    
                    # أخذ بيانات التلميذ من أول مخالفة محددة
                    first_violation = selected_violations[0]
                    student_code = first_violation.get('student_code', '')
                    student_name = first_violation.get('student_name', '')
                    
                    if not student_code or not student_name:
                        QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات التلميذ في المخالفة المحددة!")
                        return
                    
                    # التحقق من تطابق بيانات التلميذ في جميع المخالفات المحددة
                    for violation in selected_violations:
                        if (violation.get('student_code') != student_code or 
                            violation.get('student_name') != student_name):
                            QMessageBox.warning(self, "تنبيه", 
                                "المخالفات المحددة تحتوي على تلاميذ مختلفين!\n"
                                "يرجى تحديد مخالفات لنفس التلميذ فقط.")
                            return
                    
                    # تحضير بيانات التلميذ
                    student_data = {
                        'code': student_code,
                        'name': student_name
                    }
                    
                    # طباعة سجل المخالفات للتلميذ
                    self.print_selected_student_record(student_data)
                    
                except Exception as e:
                    print(f"خطأ في معالجة المخالفات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ في معالجة المخالفات المحددة:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_violations)
            
        except Exception as e:
            print(f"خطأ في طباعة سجل مخالفات التلميذ: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")

    def print_selected_student_record(self, student_data):
        """طباعة سجل مخالفات التلميذ المحدد"""
        try:
            student_code = student_data['code']
            student_name = student_data['name']
            
            # الحصول على جميع مخالفات التلميذ من قاعدة البيانات
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # الحصول على معلومات التلميذ
            cursor.execute('''
                SELECT DISTINCT رمز_التلميذ, اسم_التلميذ, المستوى, القسم
                FROM المخالفات WHERE رمز_التلميذ = ? AND اسم_التلميذ = ?
                LIMIT 1
            ''', (student_code, student_name))
            
            student_row = cursor.fetchone()
            if not student_row:
                conn.close()
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات التلميذ في قاعدة البيانات!")
                return
            
            # تحضير معلومات التلميذ
            student_info = {
                'code': student_row[0] or '',
                'name': student_row[1] or '',
                'level': student_row[2] or '',
                'section': student_row[3] or ''
            }
            
            # الحصول على جميع مخالفات التلميذ مرتبة حسب التاريخ
            cursor.execute('''
                SELECT id, التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم,
                       المادة, الأستاذ, الملاحظات, الإجراءات,
                       السنة_الدراسية, الأسدس
                FROM المخالفات 
                WHERE رمز_التلميذ = ? AND اسم_التلميذ = ?
                ORDER BY التاريخ ASC
            ''', (student_code, student_name))
            
            violations_rows = cursor.fetchall()
            conn.close()
            
            if not violations_rows:
                QMessageBox.information(self, "تنبيه", "لا توجد مخالفات مسجلة لهذا التلميذ!")
                return
            
            # تحضير سجل المخالفات
            violations_records = []
            for row in violations_rows:
                violation_record = {
                    'id': row[0],
                    'date': row[1] or '',
                    'student_code': row[2] or '',
                    'student_name': row[3] or '',
                    'level': row[4] or '',
                    'section': row[5] or '',
                    'subject': row[6] or '',
                    'teacher': row[7] or '',
                    'notes': row[8] or '',
                    'procedures': row[9] or '',
                    'academic_year': row[10] or '',
                    'semester': row[11] or ''
                }
                violations_records.append(violation_record)
            
            # استدعاء دالة الطباعة من print3.py
            try:
                success = print3.print_student_violations_record(
                    student_info=student_info,
                    violations_records=violations_records,
                    db_path="data.db"
                )
                
                if success:
                    self.status_bar.showMessage(f"تمت طباعة سجل مخالفات التلميذ '{student_name}' بنجاح ✅")
                    QMessageBox.information(self, "نجح", 
                        f"تم إنشاء ملف سجل المخالفات للتلميذ '{student_name}' بنجاح! 📋\n"
                        f"عدد المخالفات: {len(violations_records)}")
                else:
                    self.status_bar.showMessage("فشلت طباعة سجل المخالفات ❌")
                    QMessageBox.warning(self, "خطأ", "فشل في إنشاء ملف سجل المخالفات!")
                    
            except Exception as print_error:
                print(f"خطأ في طباعة سجل المخالفات: {print_error}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة سجل المخالفات:\n{str(print_error)}")
                
        except Exception as e:
            print(f"خطأ في معالجة سجل المخالفات للطباعة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        event.accept()

    def ensure_maximized(self):
        """دالة مساعدة لضمان فتح النافذة في كامل الشاشة"""
        self.showMaximized()
        self.activateWindow()
        self.raise_()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = ViolationsViewerWindow()
    window.ensure_maximized()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
