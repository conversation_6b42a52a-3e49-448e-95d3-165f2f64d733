#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sqlite3
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView


class AbsenceJustificationHTMLWindow(QMainWindow):
    """نافذة تبرير الغياب باستخدام منهجية Python + HTML الحديثة"""
    
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.current_student = None
        
        # تهيئة القيم الافتراضية
        self.school_year = "2024/2025"
        self.semester = "الأول"
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
        # إنشاء HTML وعرضه
        self.generate_html()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🏥")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين حجم النافذة
        screen = QApplication.desktop().screenGeometry()
        width = int(screen.width() * 0.9)
        height = int(screen.height() * 0.85)
        self.resize(width, height)
        
        # توسيط النافذة
        self.move(
            (screen.width() - width) // 2,
            (screen.height() - height) // 2
        )
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(80)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel("🏥 تبرير الغياب")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()

        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            print("🔄 بدء تحميل البيانات الأولية...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print("✅ جدول بيانات_المؤسسة موجود")
                
                # فحص الأعمدة الموجودة في الجدول
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns_info = cursor.fetchall()
                available_columns = [col[1] for col in columns_info]
                print(f"📋 الأعمدة المتاحة في جدول بيانات_المؤسسة: {available_columns}")
                
                # تحميل السنة الدراسية والأسدس
                try:
                    cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
                    result = cursor.fetchone()
                    
                    if result:
                        self.school_year = str(result[0]) if result[0] else "2024/2025"
                        self.semester = str(result[1]) if result[1] else "الأول"
                        
                        print(f"✅ تم تحميل السنة الدراسية: {self.school_year}")
                        print(f"✅ تم تحميل الأسدس: {self.semester}")
                    else:
                        print("⚠️ لا توجد بيانات في جدول بيانات_المؤسسة")
                        self.school_year = "2024/2025"
                        self.semester = "الأول"
                        
                except sqlite3.Error as db_error:
                    print(f"❌ خطأ في استعلام بيانات_المؤسسة: {db_error}")
                    # محاولة استعلام بديل
                    try:
                        cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
                        result = cursor.fetchone()
                        print(f"📊 بيانات الجدول الأولى: {result}")
                        
                        # تعيين القيم الافتراضية
                        self.school_year = "2024/2025"
                        self.semester = "الأول"
                        
                    except Exception as inner_error:
                        print(f"❌ خطأ في الاستعلام البديل: {inner_error}")
                        self.school_year = "2024/2025"
                        self.semester = "الأول"
            else:
                print("❌ جدول بيانات_المؤسسة غير موجود")
                self.school_year = "2024/2025"
                self.semester = "الأول"
            
            conn.close()
            
            # تحديث شريط الحالة
            self.status_bar.showMessage(f"تم تحميل البيانات: السنة الدراسية {self.school_year} - الأسدس {self.semester}")
            print(f"🎯 النتيجة النهائية - السنة الدراسية: {self.school_year}, الأسدس: {self.semester}")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات الأولية: {e}")
            import traceback
            traceback.print_exc()
            
            # تعيين القيم الافتراضية في حالة الخطأ
            self.school_year = "2024/2025"
            self.semester = "الأول"
            
            # تحديث شريط الحالة
            self.status_bar.showMessage(f"تم استخدام القيم الافتراضية: السنة الدراسية {self.school_year} - الأسدس {self.semester}")

    def generate_html(self):
        """توليد HTML لعرض النموذج"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة تبرير الغياب</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
            direction: rtl;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-family: 'Calibri', sans-serif;
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-family: 'Calibri', sans-serif;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .search-section {{
            background: #e8f4fd;
            padding: 20px;
            border-bottom: 3px solid #3498db;
        }}
        
        .search-container {{
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }}
        
        .search-container label {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
        }}
        
        .search-input {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            padding: 12px 20px;
            border: 2px solid #3498db;
            border-radius: 10px;
            min-width: 200px;
            background: white;
            transition: all 0.3s ease;
        }}
        
        .search-input:focus {{
            border-color: #2980b9;
            outline: none;
            box-shadow: 0 0 15px rgba(52, 152, 219, 0.4);
        }}
        
        .search-btn {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            padding: 12px 24px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .search-btn:hover {{
            background: linear-gradient(45deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }}
        
        .main-content {{
            display: flex;
            flex-direction: column;
            gap: 30px;
            padding: 30px;
        }}
        
        .form-section {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }}
        
        .section-title {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }}
        
        .form-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }}
        
        .form-row {{
            display: flex;
            flex-direction: column;
            gap: 5px;
        }}
        
        .form-row.full-width {{
            grid-column: 1 / -1;
        }}
        
        label {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
        }}
        
        h3 {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
            margin: 20px 0 15px;
        }}
        
        input[type="text"], input[type="date"], input[type="number"], 
        textarea, select {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: white;
        }}
        
        input[type="text"]:focus, input[type="date"]:focus, 
        input[type="number"]:focus, textarea:focus, select:focus {{
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }}
        
        input[readonly] {{
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }}
        
        /* إخفاء علامة X من مربعات التاريخ */
        input[type="date"]::-webkit-clear-button,
        input[type="date"]::-webkit-inner-spin-button {{
            display: none;
        }}
        
        input[type="date"]::-webkit-calendar-picker-indicator {{
            opacity: 1;
        }}
        
        textarea {{
            resize: vertical;
            min-height: 80px;
        }}
        
        .button-group {{
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }}
        
        .btn {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }}
        
        .btn-success {{
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }}
        
        .btn-success:hover {{
            background: linear-gradient(45deg, #219a52, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }}
        
        .alert {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }}
        
        .alert-success {{
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }}
        
        .alert-danger {{
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }}
        
        .alert-warning {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }}
        
        .loading {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            display: none;
            text-align: center;
            padding: 20px;
        }}
        
        .spinner {{
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }}
        
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        
        @media (max-width: 768px) {{
            .main-content {{
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }}
            
            .form-grid {{
                grid-template-columns: 1fr;
            }}
            
            .search-container {{
                flex-direction: column;
            }}
            
            .search-input {{
                min-width: 100%;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 تبرير الغياب</h1>
            <p>نموذج تبرير الغياب للتلاميذ</p>
        </div>
        
        <!-- قسم البحث الجديد -->
        <div class="search-section">
            <div class="search-container">
                <label for="student_search">🔍 البحث برمز التلميذ:</label>
                <input type="text" id="student_search" class="search-input" 
                       placeholder="أدخل رمز التلميذ..." 
                       onkeypress="handleEnterKey(event)">
                <button type="button" class="search-btn" onclick="searchStudent()">
                    🔍 بحث
                </button>
            </div>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h2 class="section-title">📋 معلومات التبرير</h2>
                
                <div id="alerts"></div>
                
                <!-- معلومات التلميذ -->
                <div class="form-grid">
                    <div class="form-row">
                        <label>رمز التلميذ:</label>
                        <input type="text" id="student_code" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>الاسم والنسب:</label>
                        <input type="text" id="student_name" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>الرقم الترتيبي:</label>
                        <input type="text" id="student_id" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>المستوى:</label>
                        <input type="text" id="level" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>القسم:</label>
                        <input type="text" id="class_name" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>السنة الدراسية:</label>
                        <input type="text" id="school_year" value="{self.school_year}" readonly>
                    </div>
                    <div class="form-row">
                        <label>الأسدس:</label>
                        <input type="text" id="semester" value="{self.semester}" readonly>
                    </div>
                </div>
                
                <!-- معلومات التبرير -->
                <h3>📅 تفاصيل الغياب</h3>
                <div class="form-grid">
                    <div class="form-row">
                        <label>تاريخ التبرير:</label>
                        <input type="date" id="justification_date" value="{current_date}">
                    </div>
                    <div class="form-row">
                        <label>تاريخ بداية الغياب:</label>
                        <input type="date" id="start_date" value="{current_date}" onchange="calculateEndDate()">
                    </div>
                    <div class="form-row">
                        <label>تاريخ نهاية الغياب:</label>
                        <input type="date" id="end_date" value="{current_date}">
                    </div>
                    <div class="form-row">
                        <label>عدد الأيام:</label>
                        <input type="number" id="days_count" value="1" min="1" max="365" onchange="calculateEndDate()">
                    </div>
                    <div class="form-row full-width">
                        <label>سبب الغياب:</label>
                        <input type="text" id="reason" value="شهادة طبية" placeholder="أدخل سبب الغياب">
                    </div>
                    <div class="form-row full-width">
                        <label>ملاحظات إضافية:</label>
                        <textarea id="notes" placeholder="يمكنك إضافة ملاحظات إضافية هنا..."></textarea>
                    </div>
                </div>
                
                <div class="button-group">
                    <button class="btn btn-success" onclick="saveJustification()">
                        💾 حفظ التبرير
                    </button>
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري حفظ البيانات...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // متغير عام لتخزين طلبات الحفظ
        window.pendingSaveData = null;
        
        function handleEnterKey(event) {{
            if (event.key === 'Enter') {{
                searchStudent();
            }}
        }}
        
        function searchStudent() {{
            const studentCode = document.getElementById('student_search').value.trim();
            if (!studentCode) {{
                showAlert('يرجى إدخال رمز التلميذ', 'warning');
                return;
            }}
            
            showAlert('جاري البحث في قاعدة البيانات المركزية...', 'warning');
            
            // تعيين علامة طلب البحث للمعالجة
            const searchInput = document.getElementById('student_search');
            searchInput.dataset.searchRequested = 'true';
            searchInput.dataset.searchCode = studentCode;
        }}
        
        function calculateEndDate() {{
            const startDate = document.getElementById('start_date').value;
            const daysCount = parseInt(document.getElementById('days_count').value) || 1;
            
            if (startDate) {{
                const start = new Date(startDate);
                const end = new Date(start);
                end.setDate(start.getDate() + daysCount - 1);
                
                const endDateStr = end.toISOString().split('T')[0];
                document.getElementById('end_date').value = endDateStr;
            }}
        }}
        
        function showAlert(message, type) {{
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${{type}}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            alertsContainer.innerHTML = '';
            alertsContainer.appendChild(alert);
            
            setTimeout(() => {{
                alert.style.display = 'none';
            }}, 5000);
        }}
        
        function validateForm() {{
            const studentCode = document.getElementById('student_code').value.trim();
            const studentName = document.getElementById('student_name').value.trim();
            const reason = document.getElementById('reason').value.trim();
            
            if (!studentCode || !studentName) {{
                showAlert('الرجاء البحث عن تلميذ أولاً', 'danger');
                return false;
            }}
            
            if (!reason) {{
                showAlert('الرجاء إدخال سبب الغياب', 'danger');
                return false;
            }}
            
            return true;
        }}
        
        function saveJustification() {{
            console.log('💾 بدء عملية الحفظ من JavaScript...');
            
            if (!validateForm()) {{
                return;
            }}
            
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // جمع البيانات
            const data = {{
                student_code: document.getElementById('student_code').value,
                student_name: document.getElementById('student_name').value,
                student_id: document.getElementById('student_id').value,
                level: document.getElementById('level').value,
                class_name: document.getElementById('class_name').value,
                school_year: document.getElementById('school_year').value,
                semester: document.getElementById('semester').value,
                justification_date: document.getElementById('justification_date').value,
                start_date: document.getElementById('start_date').value,
                end_date: document.getElementById('end_date').value,
                days_count: document.getElementById('days_count').value,
                reason: document.getElementById('reason').value,
                notes: document.getElementById('notes').value
            }};
            
            console.log('📋 البيانات المجمعة:', data);
            
            // محاولة الإرسال عبر QWebChannel أولاً
            if (window.bridge && window.bridge.saveJustification) {{
                console.log('📡 إرسال عبر QWebChannel...');
                try {{
                    window.bridge.saveJustification(JSON.stringify(data));
                    return;
                }} catch(e) {{
                    console.error('❌ خطأ في QWebChannel:', e);
                }}
            }}
            
            // الطريقة البديلة: حفظ في متغير عام ليتم قراءته من Python
            console.log('📡 استخدام الطريقة البديلة...');
            window.pendingSaveData = data;
            
            console.log('✅ تم تعيين البيانات للحفظ');
        }}
        
        function setStudentInfo(code, name, id, level, className) {{
            try {{
                console.log('🔄 بدء تعيين معلومات التلميذ:', {{code, name, id, level, className}});

                const elements = {{
                    'student_code': code || '',
                    'student_name': name || '',
                    'student_id': id || '',
                    'level': level || '',
                    'class_name': className || ''
                }};

                for (const [elementId, value] of Object.entries(elements)) {{
                    const element = document.getElementById(elementId);
                    if (element) {{
                        element.value = value;
                        console.log(`✅ تم تعيين ${{elementId}}: ${{value}}`);
                    }} else {{
                        console.error(`❌ لم يتم العثور على العنصر: ${{elementId}}`);
                    }}
                }}

                // تعيين نفس القيمة في مربع البحث
                const searchInput = document.getElementById('student_search');
                if (searchInput) {{
                    searchInput.value = code || '';
                }}

                console.log('✅ تم تعيين جميع معلومات التلميذ بنجاح');

                // إظهار رسالة نجاح
                showAlert(`تم العثور على التلميذ: ${{name}}`, 'success');

            }} catch(error) {{
                console.error('❌ خطأ في تعيين معلومات التلميذ:', error);
                showAlert('حدث خطأ في تحديد معلومات التلميذ', 'danger');
            }}
        }}
        
        function clearStudentInfo() {{
            const elements = ['student_code', 'student_name', 'student_id', 'level', 'class_name', 'student_search'];
            elements.forEach(elementId => {{
                const element = document.getElementById(elementId);
                if (element) {{
                    element.value = '';
                }}
            }});
        }}
        
        // إضافة دالة clearForm المحسنة
        function clearForm() {{
            // مسح جميع حقول النموذج
            document.getElementById('notes').value = '';
            document.getElementById('days_count').value = '1';
            document.getElementById('reason').value = 'شهادة طبية';
            
            // إعادة تعيين التواريخ للتاريخ الحالي
            const currentDate = new Date().toISOString().split('T')[0];
            document.getElementById('justification_date').value = currentDate;
            document.getElementById('start_date').value = currentDate;
            document.getElementById('end_date').value = currentDate;
            
            calculateEndDate();
        }}
        
        // دالة تأكيد نجاح الحفظ
        function confirmSaveSuccess() {{
            const loading = document.getElementById('loading');
            loading.style.display = 'none';
            
            // مسح جميع البيانات فوراً
            clearForm(); 
            clearStudentInfo();
            
            showAlert('تم حفظ التبرير بنجاح وتم مسح النموذج! ✅', 'success');
        }}
        
        // دالة تأكيد فشل الحفظ
        function confirmSaveError(message) {{
            const loading = document.getElementById('loading');
            loading.style.display = 'none';
            showAlert(message || 'فشل في حفظ التبرير ❌', 'danger');
        }}
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {{
            calculateEndDate();
            console.log('📄 تم تحميل الصفحة وتهيئة الوظائف');
        }});
    </script>
</body>
</html>
        """
        
        self.web_view.setHtml(html_content)
        
        # إضافة قناة للتواصل مع JavaScript
        self.setup_js_bridge()

    def setup_js_bridge(self):
        """إعداد قناة التواصل مع JavaScript مع دعم الاستعلام المركزي"""
        try:
            print("🔧 إعداد قناة التواصل مع JavaScript (مع الاستعلام المركزي)...")

            # إعداد مؤقت للتحقق من طلبات البحث المركزي
            self.search_timer = QTimer()
            self.search_timer.timeout.connect(self.check_central_search_requests)
            self.search_timer.start(500)  # فحص كل نصف ثانية

            # إعداد مؤقت للتحقق من طلبات الحفظ
            self.save_timer = QTimer()
            self.save_timer.timeout.connect(self.check_save_requests)
            self.save_timer.start(300)  # فحص كل 300 مللي ثانية

            # محاولة إعداد QWebChannel كطريقة إضافية
            try:
                from PyQt5.QtWebChannel import QWebChannel
                self.channel = QWebChannel()
                
                # إنشاء كائن Bridge للتواصل مع JavaScript
                self.js_bridge = JSBridge(self)
                self.channel.registerObject("bridge", self.js_bridge)
                self.web_view.page().setWebChannel(self.channel)
                print("✅ تم إعداد QWebChannel مع كائن Bridge")
            except ImportError:
                print("⚠️ QWebChannel غير متاح - سيتم استخدام النظام البديل")

        except Exception as e:
            print(f"❌ خطأ في إعداد قناة JavaScript مع الاستعلام المركزي: {e}")

    def check_central_search_requests(self):
        """فحص طلبات البحث المركزي من JavaScript"""
        try:
            # الحصول على طلب البحث من JavaScript
            js_code = """
                (function() {
                    const searchInput = document.getElementById('student_search');
                    if (searchInput && searchInput.dataset.searchRequested === 'true') {
                        const searchCode = searchInput.dataset.searchCode;
                        searchInput.dataset.searchRequested = 'false';
                        return searchCode;
                    }
                    return '';
                })();
            """
            
            def handle_central_search_result(result):
                if result and result.strip():
                    print(f"🔍 تم استلام طلب البحث المركزي: {result}")
                    # تنفيذ البحث المركزي
                    self.search_student_by_code(result.strip())
            
            self.web_view.page().runJavaScript(js_code, handle_central_search_result)
            
        except Exception as e:
            print(f"❌ خطأ في فحص طلبات البحث المركزي: {e}")

    def check_save_requests(self):
        """فحص طلبات الحفظ من JavaScript"""
        try:
            # الحصول على طلب الحفظ من JavaScript
            js_code = """
                (function() {
                    if (window.pendingSaveData) {
                        const data = window.pendingSaveData;
                        window.pendingSaveData = null;
                        return JSON.stringify(data);
                    }
                    return '';
                })();
            """
            
            def handle_save_request(result):
                if result and result.strip():
                    print(f"📥 تم استلام طلب حفظ: {result}")
                    self.handle_js_save_request(result)
            
            self.web_view.page().runJavaScript(js_code, handle_save_request)
            
        except Exception as e:
            print(f"❌ خطأ في فحص طلبات الحفظ: {e}")

    def search_student_by_code(self, student_code):
        """البحث عن التلميذ برمزه باستخدام الاستعلام المركزي"""
        try:
            print(f"🔍 البحث عن التلميذ برمزه: {student_code} (الاستعلام المركزي)")
            print(f"🔍 مسار قاعدة البيانات: {self.db_path}")
            print(f"🔍 السنة الدراسية الحالية: {self.school_year}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تشخيص أولي
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            available_tables = [table[0] for table in tables]
            print(f"🔍 الجداول المتاحة: {available_tables}")

            # فحص وجود الجداول المطلوبة
            if 'السجل_العام' not in available_tables:
                print("❌ جدول السجل العام غير موجود!")
                js_code = f"showAlert('خطأ: جدول السجل العام غير موجود', 'danger');"
                self.web_view.page().runJavaScript(js_code)
                conn.close()
                return False

            if 'اللوائح' not in available_tables:
                print("❌ جدول اللوائح غير موجود!")
                js_code = f"showAlert('خطأ: جدول اللوائح غير موجود', 'danger');"
                self.web_view.page().runJavaScript(js_code)
                conn.close()
                return False

            # الاستعلام المركزي الأساسي (كما هو في sub85_window.py)
            central_query_1 = """
            SELECT 
                s.الرمز,
                s.الاسم_والنسب,
                l.المستوى,
                l.القسم,
                l.رت as الرقم_الترتيبي
            FROM السجل_العام s
            LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
            WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
            LIMIT 1
            """
            
            print(f"📋 تنفيذ الاستعلام المركزي الأول للرمز: {student_code}, السنة: {self.school_year}")
            cursor.execute(central_query_1, (student_code, self.school_year))
            result = cursor.fetchone()
            print(f"📊 نتيجة الاستعلام الأول: {result}")

            # إذا لم نحصل على نتائج، جرب استعلامات بديلة
            if not result:
                print("⚠️ الاستعلام الأول لم يعطي نتائج، تجربة استعلامات بديلة...")
                
                # الاستعلام الثاني: البحث بدون شرط السنة الدراسية
                central_query_2 = """
                SELECT 
                    s.الرمز,
                    s.الاسم_والنسب,
                    l.المستوى,
                    l.القسم,
                    l.رت as الرقم_الترتيبي
                FROM السجل_العام s
                LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE s.الرمز = ? AND l.السنة_الدراسية IS NOT NULL
                ORDER BY l.السنة_الدراسية DESC
                LIMIT 1
                """
                
                print(f"📋 تنفيذ الاستعلام المركزي الثاني للرمز: {student_code}")
                cursor.execute(central_query_2, (student_code,))
                result = cursor.fetchone()
                print(f"📊 نتيجة الاستعلام الثاني: {result}")
                
                if not result:
                    # الاستعلام الثالث: من جدول اللوائح فقط للسنة الحالية
                    central_query_3 = """
                    SELECT 
                        l.الرمز,
                        'غير محدد' as الاسم_والنسب,
                        l.المستوى,
                        l.القسم,
                        l.رت as الرقم_الترتيبي
                    FROM اللوائح l
                    WHERE l.الرمز = ? AND l.السنة_الدراسية = ?
                    LIMIT 1
                    """
                    
                    print(f"📋 تنفيذ الاستعلام المركزي الثالث للرمز: {student_code}")
                    cursor.execute(central_query_3, (student_code, self.school_year))
                    result = cursor.fetchone()
                    print(f"📊 نتيجة الاستعلام الثالث: {result}")
                    
                    if not result:
                        # الاستعلام الرابع: من السجل العام فقط
                        central_query_4 = """
                        SELECT 
                            s.الرمز,
                            s.الاسم_والنسب,
                            'غير محدد' as المستوى,
                            'غير محدد' as القسم,
                            'غير محدد' as الرقم_الترتيبي
                        FROM السجل_العام s
                        WHERE s.الرمز = ?
                        LIMIT 1
                        """
                        
                        print(f"📋 تنفيذ الاستعلام المركزي الرابع للرمز: {student_code}")
                        cursor.execute(central_query_4, (student_code,))
                        result = cursor.fetchone()
                        print(f"📊 نتيجة الاستعلام الرابع: {result}")

            conn.close()
            
            if result:
                # استخراج البيانات من النتيجة
                code = result[0] if result[0] else "غير محدد"
                name = result[1] if result[1] else "غير محدد"
                level = result[2] if result[2] else "غير محدد"
                class_name = result[3] if result[3] else "غير محدد"
                student_id = result[4] if result[4] else "غير محدد"
                
                print(f"✅ تم العثور على التلميذ: {name}")
                print(f"📋 التفاصيل: الرمز={code}, الاسم={name}, المستوى={level}, القسم={class_name}, الرقم الترتيبي={student_id}")
                
                # تعيين معلومات التلميذ في HTML باستخدام البيانات المركزية
                js_code = f"""
                    try {{
                        console.log('🔄 تعيين بيانات من الاستعلام المركزي');
                        
                        // تعيين البيانات المطلوبة
                        document.getElementById('student_code').value = '{code}';
                        document.getElementById('student_name').value = '{name}';
                        document.getElementById('level').value = '{level}';
                        document.getElementById('class_name').value = '{class_name}';
                        document.getElementById('student_id').value = '{student_id}';
                        
                        // تعيين نفس القيمة في مربع البحث
                        document.getElementById('student_search').value = '{code}';
                        
                        // عرض رسالة نجاح مفصلة
                        const successMessage = `تم العثور على التلميذ بنجاح\\n` +
                                             `الاسم: {name}\\n` +
                                             `المستوى: {level} | القسم: {class_name}\\n` +
                                             `الرقم الترتيبي: {student_id}`;
                        
                        showAlert(successMessage, 'success');
                        
                        console.log('✅ تم تعيين البيانات من الاستعلام المركزي بنجاح');
                        
                    }} catch(e) {{
                        console.error('❌ خطأ في تعيين البيانات من الاستعلام المركزي:', e);
                        showAlert('تم العثور على التلميذ لكن حدث خطأ في العرض', 'warning');
                    }}
                """
                self.web_view.page().runJavaScript(js_code)
                
                # حفظ البيانات المركزية في الكائن الحالي
                self.current_student = {
                    'code': code,
                    'name': name,
                    'id': student_id,
                    'level': level,
                    'class': class_name
                }
                
                self.status_bar.showMessage(f"تم العثور على التلميذ بالاستعلام المركزي: {name}")
                print(f"✅ تم البحث بنجاح باستخدام الاستعلام المركزي")
                return True
                
            else:
                print(f"❌ لم يتم العثور على التلميذ برمز: {student_code} في جميع الاستعلامات المركزية")
                js_code = f"""
                    // مسح البيانات السابقة
                    document.getElementById('student_code').value = '';
                    document.getElementById('student_name').value = '';
                    document.getElementById('level').value = '';
                    document.getElementById('class_name').value = '';
                    document.getElementById('student_id').value = '';
                    
                    showAlert('لم يتم العثور على تلميح برمز: {student_code} في قاعدة البيانات', 'danger');
                """
                self.web_view.page().runJavaScript(js_code)
                
                self.status_bar.showMessage(f"لم يتم العثور على تلميح برمز: {student_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاستعلام المركزي للبحث عن التلميذ: {e}")
            import traceback
            traceback.print_exc()
            
            js_code = f"""
                showAlert('حدث خطأ أثناء البحث المركزي: {str(e)}', 'danger');
            """
            self.web_view.page().runJavaScript(js_code)
            return False

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        # إيقاف جميع المؤقتات
        if hasattr(self, 'search_timer'):
            self.search_timer.stop()
        if hasattr(self, 'save_timer'):
            self.save_timer.stop()
        event.accept()

    def save_justification(self):
        """حفظ تبرير الغياب في قاعدة البيانات"""
        try:
            print("💾 بدء عملية حفظ التبرير...")
            
            # التحقق من وجود بيانات التلميذ
            if not self.current_student:
                QMessageBox.warning(self, "تحذير", "يرجى البحث عن تلميذ أولاً")
                return False
            
            # إنشاء حوار لجمع بيانات التبرير
            dialog = QDialog(self)
            dialog.setWindowTitle("💾 حفظ تبرير الغياب")
            dialog.setModal(True)
            dialog.resize(400, 300)
            
            layout = QVBoxLayout(dialog)
            
            # عرض معلومات التلميذ
            info_label = QLabel(f"التلميذ: {self.current_student.get('name', 'غير محدد')}")
            info_label.setFont(QFont("Calibri", 12, QFont.Bold))
            layout.addWidget(info_label)
            
            # حقول البيانات
            reason_label = QLabel("سبب الغياب:")
            reason_input = QLineEdit("شهادة طبية")
            
            start_date_label = QLabel("تاريخ بداية الغياب:")
            start_date_input = QDateEdit(QDate.currentDate())
            start_date_input.setCalendarPopup(True)
            
            end_date_label = QLabel("تاريخ نهاية الغياب:")
            end_date_input = QDateEdit(QDate.currentDate())
            end_date_input.setCalendarPopup(True)
            
            notes_label = QLabel("ملاحظات:")
            notes_input = QTextEdit()
            notes_input.setMaximumHeight(80)
            
            layout.addWidget(reason_label)
            layout.addWidget(reason_input)
            layout.addWidget(start_date_label)
            layout.addWidget(start_date_input)
            layout.addWidget(end_date_label)
            layout.addWidget(end_date_input)
            layout.addWidget(notes_label)
            layout.addWidget(notes_input)
            
            # أزرار التحكم
            buttons_layout = QHBoxLayout()
            save_btn = QPushButton("💾 حفظ")
            cancel_btn = QPushButton("❌ إلغاء")
            
            save_btn.clicked.connect(dialog.accept)
            cancel_btn.clicked.connect(dialog.reject)
            
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            layout.addLayout(buttons_layout)
            
            if dialog.exec_() == QDialog.Accepted:
                # حفظ البيانات
                self.save_to_database({
                    'student_code': self.current_student.get('code', ''),
                    'student_name': self.current_student.get('name', ''),
                    'reason': reason_input.text(),
                    'start_date': start_date_input.date().toString('yyyy-MM-dd'),
                    'end_date': end_date_input.date().toString('yyyy-MM-dd'),
                    'notes': notes_input.toPlainText()
                })
                
                QMessageBox.information(self, "نجح", "تم حفظ التبرير بنجاح!")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التبرير: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ التبرير:\n{str(e)}")
            return False

    def handle_js_save_request(self, data_json):
        """معالجة طلب الحفظ من JavaScript"""
        try:
            print(f"📥 استلام طلب حفظ من JavaScript: {data_json}")
            
            import json
            data = json.loads(data_json)
            
            # التحقق من صحة البيانات
            if not data.get('student_code') or not data.get('student_name'):
                js_code = "confirmSaveError('الرجاء البحث عن تلميذ أولاً');"
                self.web_view.page().runJavaScript(js_code)
                return False
            
            # حفظ البيانات
            success = self.save_to_database(data)
            
            if success:
                # مسح البيانات المحفوظة في Python فوراً
                self.current_student = None
                
                # رسالة نجاح محسنة مع تفاصيل أكثر
                student_name = data.get('student_name', 'غير محدد')
                start_date = data.get('start_date', '')
                end_date = data.get('end_date', '')
                days_count = data.get('days_count', '1')
                reason = data.get('reason', 'غير محدد')
                
                js_code = f"""
                    document.getElementById('loading').style.display = 'none';
                    
                    // مسح النموذج فوراً بعد النجاح
                    clearForm(); 
                    clearStudentInfo();
                    
                    // رسالة نجاح مفصلة
                    const successMessage = `تم حفظ تبرير الغياب بنجاح! ✅\\n\\n` +
                                         `📋 تفاصيل التبرير:\\n` +
                                         `👤 التلميذ: {student_name}\\n` +
                                         `📅 من تاريخ: {start_date}\\n` +
                                         `📅 إلى تاريخ: {end_date}\\n` +
                                         `🗓️ عدد الأيام: {days_count}\\n` +
                                         `📝 السبب: {reason}\\n\\n` +
                                         `💾 تم حفظ البيانات وتم مسح النموذج للإدخال التالي`;
                    
                    showAlert(successMessage, 'success');
                """
            else:
                js_code = """
                    document.getElementById('loading').style.display = 'none';
                    showAlert('❌ فشل في حفظ التبرير في قاعدة البيانات\\n\\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني', 'danger');
                """
            
            self.web_view.page().runJavaScript(js_code)
            return success
            
        except Exception as e:
            print(f"❌ خطأ في معالجة طلب الحفظ: {e}")
            js_code = f"""
                document.getElementById('loading').style.display = 'none';
                showAlert('❌ حدث خطأ تقني أثناء الحفظ:\\n\\n{str(e)}\\n\\nيرجى المحاولة مرة أخرى', 'danger');
            """
            self.web_view.page().runJavaScript(js_code)
            return False

    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات - تم إزالة دعم الصور"""
        try:
            print("💾 بدء حفظ البيانات في قاعدة البيانات...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص هيكل الجدول الموجود أولاً
            cursor.execute("PRAGMA table_info(تبريرات_الغياب)")
            columns_info = cursor.fetchall()
            existing_columns = [col[1] for col in columns_info]
            print(f"🔍 الأعمدة الموجودة في الجدول: {existing_columns}")
            
            # إنشاء الجدول إذا لم يكن موجوداً أو تحديثه
            if not existing_columns:
                print("📋 إنشاء جدول تبريرات_الغياب جديد...")
                cursor.execute("""
                    CREATE TABLE تبريرات_الغياب (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        رمز_التلميذ TEXT,
                        اسم_التلميذ TEXT,
                        ر_ت TEXT,
                        المستوى TEXT,
                        القسم TEXT,
                        تاريخ_التبرير TEXT,
                        تاريخ_البداية TEXT,
                        تاريخ_النهاية TEXT,
                        عدد_الأيام INTEGER,
                        عدد_الساعات INTEGER,
                        سبب_الغياب TEXT,
                        ملاحظات TEXT,
                        مسار_الصورة TEXT,
                        تاريخ_التسجيل TEXT,
                        السنة_الدراسية TEXT,
                        الأسدس TEXT
                    )
                """)
                print("✅ تم إنشاء الجدول بنجاح")
            
            # حساب عدد الأيام
            try:
                from datetime import datetime
                start_date = datetime.strptime(data.get('start_date', ''), '%Y-%m-%d')
                end_date = datetime.strptime(data.get('end_date', ''), '%Y-%m-%d')
                days_count = (end_date - start_date).days + 1;
            except:
                days_count = int(data.get('days_count', 1))
            
            # تاريخ التسجيل الحالي
            current_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # إدراج البيانات باستخدام أسماء الأعمدة الصحيحة
            cursor.execute("""
                INSERT INTO تبريرات_الغياب (
                    رمز_التلميذ, اسم_التلميذ, ر_ت, المستوى, القسم,
                    تاريخ_التبرير, تاريخ_البداية, تاريخ_النهاية, عدد_الأيام,
                    سبب_الغياب, ملاحظات, مسار_الصورة, تاريخ_التسجيل,
                    السنة_الدراسية, الأسدس
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data.get('student_code', ''),
                data.get('student_name', ''),
                data.get('student_id', ''),  # ر_ت بدلاً من الرقم_الترتيبي
                data.get('level', ''),
                data.get('class_name', ''),
                data.get('justification_date', ''),
                data.get('start_date', ''),  # تاريخ_البداية بدلاً من تاريخ_بداية_الغياب
                data.get('end_date', ''),    # تاريخ_النهاية بدلاً من تاريخ_نهاية_الغياب
                days_count,
                data.get('reason', ''),
                data.get('notes', ''),
                '',  # مسار الصورة فارغ (تم إزالة دعم الصور)
                current_timestamp,  # تاريخ_التسجيل
                data.get('school_year', self.school_year),
                data.get('semester', self.semester)
            ))
            
            conn.commit();
            conn.close();
            
            print("✅ تم حفظ التبرير بنجاح في قاعدة البيانات")
            
            # تحديث شريط الحالة مع تفاصيل أكثر
            student_name = data.get('student_name', 'غير محدد')
            current_time = datetime.now().strftime('%H:%M:%S')
            self.status_bar.showMessage(f"✅ تم حفظ تبرير الغياب للتلميذ: {student_name} في {current_time}")
            
            # استدعاء دالة مسح البيانات (ستعمل فقط إذا لم يتم مسحها بالفعل من JavaScript)
            QTimer.singleShot(1000, self.clear_all_data)  # مسح بعد ثانية واحدة
            
            return True;
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            import traceback
            traceback.print_exc()
            if 'conn' in locals():
                conn.close()
            return False

    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات التلميذ"""
        try:
            print(f"📝 تعيين معلومات التلميذ: {name}")
            
            # حفظ البيانات في الكائن الحالي
            self.current_student = {
                'code': str(code) if code else '',
                'name': str(name) if name else '',
                'id': str(id_num) if id_num else '',
                'level': str(level) if level else '',
                'class': str(class_name) if class_name else ''
            }
            
            # تعيين البيانات في HTML
            # تنظيف النص من الرموز التي قد تسبب مشاكل في JavaScript
            safe_code = str(code).replace("'", "\\'").replace('"', '\\"') if code else '';
            safe_name = str(name).replace("'", "\\'").replace('"', '\\"') if name else '';
            safe_id = str(id_num).replace("'", "\\'").replace('"', '\\"') if id_num else '';
            safe_level = str(level).replace("'", "\\'").replace('"', '\\"') if level else '';
            safe_class = str(class_name).replace("'", "\\'").replace('"', '\\"') if class_name else '';
            
            js_code = f"setStudentInfo('{safe_code}', '{safe_name}', '{safe_id}', '{safe_level}', '{safe_class}');";
            self.web_view.page().runJavaScript(js_code);
            
            self.status_bar.showMessage(f"تم تحديد التلميذ: {name}")
            return True;
            
        except Exception as e:
            print(f"❌ خطأ في تعيين معلومات التلميذ: {e}")
            return False

    def clear_all_data(self):
        """مسح جميع البيانات بعد الحفظ الناجح"""
        try:
            # مسح البيانات في Python
            self.current_student = None
            
            # مسح البيانات في JavaScript
            js_code = """
                try {
                    // مسح جميع البيانات
                    clearForm(); 
                    clearStudentInfo();
                    
                    // إخفاء أي رسائل تحميل
                    const loading = document.getElementById('loading');
                    if (loading) {
                        loading.style.display = 'none';
                    }
                    
                    console.log('✅ تم مسح جميع البيانات بنجاح');
                } catch(e) {
                    console.error('❌ خطأ في مسح البيانات:', e);
                }
            """
            self.web_view.page().runJavaScript(js_code)
            
            # تحديث شريط الحالة
            self.status_bar.showMessage("تم مسح جميع البيانات - جاهز لإدخال تبرير جديد")
            
            print("✅ تم مسح جميع البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في مسح البيانات: {e}")

# إضافة كلاس JSBridge للتواصل مع JavaScript
class JSBridge(QObject):
    """كلاس للتواصل بين JavaScript و Python"""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
    
    @pyqtSlot(str)
    def saveJustification(self, data_json):
        """دالة يتم استدعاؤها من JavaScript لحفظ التبرير"""
        print(f"🌉 تم استدعاء saveJustification من JavaScript: {data_json}")
        return self.main_window.handle_js_save_request(data_json)

class AbsenceJustificationWidget(QWidget):
    """ويدجت يمكن تضمينه في واجهات أخرى"""
    
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        
        # إنشاء التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء النافذة كعنصر داخلي
        self.absence_window = AbsenceJustificationHTMLWindow(db_path, parent=self)
        
        # إضافة الويدجت المركزي
        central_widget = self.absence_window.centralWidget();
        layout.addWidget(central_widget);
    
    def set_student_info(self, code, name, id_num, level, class_name):
        """إعادة توجيه تعيين معلومات التلميذ"""
        return self.absence_window.set_student_info(code, name, id_num, level, class_name)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    import sys
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = AbsenceJustificationHTMLWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

