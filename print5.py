#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف مخصص لطباعة تقرير تبرير الغياب (print5.py)
تم فصله من ملف print1_test.py
تم تحسينه باستخدام تقنية الجداول من ReportLab
"""

import os
from datetime import datetime
import sqlite3
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
import sys
import traceback
import webbrowser
import subprocess
from PyQt5.QtWidgets import QMessageBox

# تسجيل الخطوط العربية
try:
    # محاولة تسجيل الخط العربي الأساسي
    pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
    print("تم تسجيل خط Arabic (arial.ttf) بنجاح")
except Exception as font_error:
    print(f"خطأ في تسجيل خط Arabic (arial.ttf): {font_error}")
    try:
        # محاولة استخدام مسار مطلق للخط
        script_dir = os.path.dirname(os.path.abspath(__file__))
        arial_path = os.path.join(script_dir, "arial.ttf")
        if os.path.exists(arial_path):
            pdfmetrics.registerFont(TTFont("Arabic", arial_path))
            print(f"تم تسجيل خط Arabic من المسار المطلق: {arial_path}")
        else:
            print(f"خطأ: ملف الخط غير موجود في المسار: {arial_path}")
    except Exception as alt_font_error:
        print(f"خطأ في تسجيل خط Arabic من المسار المطلق: {alt_font_error}")

# محاولة تسجيل خطوط Amiri و Calibri
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # تحديد مسارات الخطوط
    amiri_regular_path = os.path.join(script_dir, "Amiri-Regular.ttf")
    amiri_bold_path = os.path.join(script_dir, "Amiri-Bold.ttf")
    calibri_regular_path = os.path.join(script_dir, "calibri.ttf")
    calibri_bold_path = os.path.join(script_dir, "calibrib.ttf")

    # البحث عن خطوط Calibri في مجلد الخطوط في Windows
    if os.name == 'nt':  # Windows
        windows_fonts_dir = os.path.join(os.environ.get('SystemRoot', 'C:\\Windows'), 'Fonts')
        if not os.path.exists(calibri_regular_path):
            windows_calibri_path = os.path.join(windows_fonts_dir, 'calibri.ttf')
            if os.path.exists(windows_calibri_path):
                calibri_regular_path = windows_calibri_path
                print(f"تم العثور على خط Calibri في مجلد خطوط Windows: {calibri_regular_path}")

        if not os.path.exists(calibri_bold_path):
            windows_calibri_bold_path = os.path.join(windows_fonts_dir, 'calibrib.ttf')
            if os.path.exists(windows_calibri_bold_path):
                calibri_bold_path = windows_calibri_bold_path
                print(f"تم العثور على خط Calibri Bold في مجلد خطوط Windows: {calibri_bold_path}")

    # تسجيل خطوط Amiri
    if os.path.exists(amiri_regular_path):
        pdfmetrics.registerFont(TTFont("Amiri", amiri_regular_path))
        print(f"تم تسجيل خط Amiri من المسار: {amiri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri في المسار: {amiri_regular_path}")

    if os.path.exists(amiri_bold_path):
        pdfmetrics.registerFont(TTFont("Amiri-Bold", amiri_bold_path))
        print(f"تم تسجيل خط Amiri-Bold من المسار: {amiri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri-Bold في المسار: {amiri_bold_path}")

    # تسجيل خطوط Calibri
    if os.path.exists(calibri_regular_path):
        pdfmetrics.registerFont(TTFont("Calibri", calibri_regular_path))
        print(f"تم تسجيل خط Calibri من المسار: {calibri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri في المسار: {calibri_regular_path}")

    if os.path.exists(calibri_bold_path):
        pdfmetrics.registerFont(TTFont("Calibri-Bold", calibri_bold_path))
        print(f"تم تسجيل خط Calibri-Bold من المسار: {calibri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri-Bold في المسار: {calibri_bold_path}")

    # إذا لم يتم العثور على أي خط، استخدم الخط العربي الأساسي
    registered_fonts = pdfmetrics.getRegisteredFontNames()
    if "Amiri" not in registered_fonts and "Amiri-Bold" not in registered_fonts and "Calibri" not in registered_fonts and "Calibri-Bold" not in registered_fonts:
        print("تحذير: لم يتم العثور على خطوط Amiri أو Calibri، سيتم استخدام الخط العربي الأساسي")
except Exception as font_error:
    print(f"خطأ في تسجيل الخطوط: {font_error}")

# Arabic text handling
import arabic_reshaper
from bidi.algorithm import get_display

def open_pdf(filename):
    """Opens the generated PDF file using the default system viewer."""
    try:
        absolute_path = os.path.abspath(filename)
        print(f"محاولة فتح الملف: {absolute_path}")

        # --- >> إضافة: معالجة الخطأ عند فتح الملف << ---
        try:
            if sys.platform == "win32":
                os.startfile(absolute_path)
            elif sys.platform == "darwin": # macOS
                os.system(f'open "{absolute_path}"')
            else: # Linux and other Unix-like
                os.system(f'xdg-open "{absolute_path}"')
        except OSError as e:
            # تحقق من رمز الخطأ الخاص بـ "No application associated"
            if hasattr(e, 'winerror') and e.winerror == 1155: # WinError 1155
                print(f"خطأ فتح الملف: لا يوجد تطبيق مرتبط لفتح ملفات PDF.")
                QMessageBox.warning(
                    None, # لا يوجد نافذة أب محددة هنا، يمكن تمريرها إذا كانت متوفرة
                    "فشل فتح الملف",
                    f"تم إنشاء ملف PDF بنجاح في:\n{absolute_path}\n\n"
                    "ولكن، لم يتم العثور على تطبيق افتراضي لفتح ملفات PDF.\n"
                    "الرجاء فتح الملف يدوياً."
                )
            else:
                # عرض الأخطاء الأخرى
                print(f"خطأ غير متوقع في فتح الملف: {e}")
                traceback.print_exc()
                QMessageBox.critical(
                    None,
                    "خطأ",
                    f"حدث خطأ أثناء محاولة فتح الملف:\n{e}"
                )
        except Exception as open_error:
            # التعامل مع أي أخطاء أخرى قد تحدث
            print(f"خطأ عام في فتح الملف: {open_error}")
            traceback.print_exc()
            QMessageBox.critical(
                None,
                "خطأ",
                f"حدث خطأ عام أثناء محاولة فتح الملف:\n{open_error}"
            )
        # --- >> نهاية الإضافة << ---

    except Exception as path_error:
        print(f"خطأ في تحديد مسار الملف: {path_error}")
        traceback.print_exc()

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

        if not os.path.exists(db_path):
            print(f"تحذير: ملف قاعدة البيانات غير موجود: {db_path}")
            return {}

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # محاولة قراءة من جدول بيانات_المؤسسة
        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    # إرجاع قاموس فارغ في حالة حدوث خطأ
    return {}

def fix_arabic(text):
    """إصلاح النص العربي للعرض في PDF"""
    if not text:
        return ""
    try:
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص: {e}")
        return str(text)

def print_absence_justification(record, db_path=None):
    """طباعة تبرير الغياب وفق المواصفات المطلوبة باستخدام تقنية الجداول"""
    try:
        # إنشاء اسم الملف
        student_name = record.get('student_name', 'بدون_اسم').replace(' ', '_')
        student_code = record.get('student_code', 'بدون_رمز')
        current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_name = f"تبرير_غياب_{student_name}_{student_code}_{current_date}.pdf"

        # إنشاء مجلد مؤقت داخل البرنامج
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        temp_file_path = os.path.join(temp_dir, file_name)

        # إنشاء مجلد رئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        if not os.path.exists(main_reports_dir):
            os.makedirs(main_reports_dir)
            print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

        # إنشاء مجلد فرعي لتقارير تبرير الغياب
        absence_reports_dir = os.path.join(main_reports_dir, "تقارير تبرير الغياب")
        if not os.path.exists(absence_reports_dir):
            os.makedirs(absence_reports_dir)
            print(f"تم إنشاء المجلد الفرعي: {absence_reports_dir}")

        # تحديد مسار الملف النهائي
        file_path = os.path.join(absence_reports_dir, file_name)

        # الحصول على معلومات المؤسسة
        institution_data = get_institution_info(db_path)

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=0.2*cm,
            leftMargin=0.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # قائمة العناصر التي سيتم إضافتها للمستند
        elements = []

        # إنشاء أنماط الفقرات
        styles = getSampleStyleSheet()

        # إنشاء نمط للنص العربي
        arabic_style = ParagraphStyle(
            'ArabicStyle',
            parent=styles['Normal'],
            fontName='Arabic',
            fontSize=12,
            alignment=1,  # وسط
            leading=14,
            rightIndent=0,
            leftIndent=0
        )

        # إنشاء نمط للعناوين
        title_style = ParagraphStyle(
            'TitleStyle',
            parent=styles['Title'],
            fontName='Calibri-Bold',
            fontSize=16,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إنشاء نمط للعناوين الفرعية
        subtitle_style = ParagraphStyle(
            'SubtitleStyle',
            parent=styles['Heading2'],
            fontName='Calibri',
            fontSize=14,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إضافة الشعار
        logo_path = institution_data.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            try:
                # إنشاء صورة بعرض 200 وارتفاع 90
                img = Image(logo_path, width=200, height=90)
                img.hAlign = 'CENTER'  # محاذاة الصورة للوسط
                elements.append(img)
                elements.append(Spacer(1, 0.1*cm))  # مسافة 0.9 سم بعد الشعار
                print(f"تم إضافة الشعار بنجاح من: {logo_path}")
            except Exception as logo_error:
                print(f"خطأ في إضافة الشعار: {logo_error}")

        # إضافة اسم المؤسسة
        institution_name = institution_data.get('name', '')
        if institution_name:
            elements.append(Paragraph(fix_arabic(institution_name), title_style))
            elements.append(Spacer(1, 0.1*cm))  # مسافة 0.9 سم بعد اسم المؤسسة

        # إضافة العنوان الرئيسي
        elements.append(Paragraph(fix_arabic("ورقة تبرير الغياب"), title_style))
        elements.append(Spacer(1, 0.1*cm))  # مسافة 0.9 سم بعد العنوان الرئيسي

        # إضافة العنوان الفرعي (السنة الدراسية)
        school_year = institution_data.get('school_year', '')
        if school_year:
            elements.append(Paragraph(fix_arabic(f"السنة الدراسية: {school_year}"), subtitle_style))
            elements.append(Spacer(1, 0.1*cm))  # مسافة 0.9 سم بعد العنوان الفرعي

        # إنشاء جدول معلومات التلميذ
        # تحضير البيانات
        student_data = [
            # الصف الأول: رمز التلميذ واسم التلميذ
            [
                fix_arabic(record.get('student_code', '')),
                fix_arabic("رمز التلميذ:"),
                fix_arabic(record.get('student_name', '')),
                fix_arabic("اسم التلميذ:")
            ],
            # الصف الثاني: المستوى والقسم
            [
                fix_arabic(record.get('level', '')),
                fix_arabic("المستوى:"),
                fix_arabic(record.get('section', '')),
                fix_arabic("القسم:")
            ]
        ]

        # إنشاء الجدول
        # تحديد عرض موحد لمسميات الأعمدة
        label_width = 3*cm  # عرض موحد لمسميات الأعمدة
        page_width = A4[0]  # عرض صفحة A4
        page_margin = 1*cm  # الهامش المستخدم في المستند (1 سم)
        available_width = page_width - 2*page_margin
        col_widths = [(available_width - 2*label_width)/2, label_width, (available_width - 2*label_width)/2, label_width]  # عرض الأعمدة
        student_table = Table(student_data, colWidths=col_widths)

        # تنسيق الجدول
        student_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            # الخط
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri'),
            ('FONTNAME', (1, 0), (1, -1), 'Calibri-Bold'),  # عناوين الأعمدة الأولى
            ('FONTNAME', (3, 0), (3, -1), 'Calibri-Bold'),  # عناوين الأعمدة الثانية
            # حجم الخط
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            # المحاذاة
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),  # قيم العمود الأول محاذاة لليسار
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # عناوين العمود الأول محاذاة لليمين
            ('ALIGN', (2, 0), (2, -1), 'LEFT'),  # قيم العمود الثاني محاذاة لليسار
            ('ALIGN', (3, 0), (3, -1), 'RIGHT'),  # عناوين العمود الثاني محاذاة لليمين
            # لون الخلفية للعناوين
            ('BACKGROUND', (1, 0), (1, -1), colors.lightgrey),
            ('BACKGROUND', (3, 0), (3, -1), colors.lightgrey),
            # المسافات الداخلية
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        # إضافة الجدول إلى المستند
        elements.append(student_table)

        # إنشاء جدول معلومات التبرير
        justification_data = [
            # تاريخ التبرير
            [
                fix_arabic(record.get('justification_date', '')),
                fix_arabic("تاريخ التبرير:")
            ],
            # فترة الغياب
            [
                fix_arabic(f"من {record.get('start_date', '')} إلى {record.get('end_date', '')}"),
                fix_arabic("فترة الغياب:")
            ],
            # عدد الأيام
            [
                fix_arabic(str(record.get('days_count', '1'))),
                fix_arabic("عدد الأيام:")
            ],
            # سبب الغياب
            [
                fix_arabic(record.get('reason', '')),
                fix_arabic("سبب الغياب:")
            ]
        ]

        # إضافة الملاحظات إذا وجدت
        if record.get('notes'):
            justification_data.append([
                fix_arabic(record.get('notes', '')),
                fix_arabic("ملاحظات:")
            ])

        # إنشاء الجدول
        # استخدام نفس عرض العمود للمسميات كما في الجدول الأول
        label_width = 3*cm  # عرض موحد لمسميات الأعمدة
        # حساب عرض العمود الأول بناءً على عرض الصفحة والهوامش
        page_width = A4[0]  # عرض صفحة A4
        page_margin = 1*cm  # الهامش المستخدم في المستند (1 سم)
        col_widths = [page_width - label_width - 2*page_margin, label_width]  # عرض الأعمدة
        justification_table = Table(justification_data, colWidths=col_widths)

        # تنسيق الجدول
        justification_table.setStyle(TableStyle([
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            # الخط
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri'),
            ('FONTNAME', (1, 0), (1, -1), 'Calibri-Bold'),  # عناوين
            # حجم الخط
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            # المحاذاة
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),  # قيم محاذاة لليسار
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # عناوين محاذاة لليمين
            # لون الخلفية للعناوين
            ('BACKGROUND', (1, 0), (1, -1), colors.lightgrey),
            # المسافات الداخلية
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        # إضافة الجدول إلى المستند
        elements.append(justification_table)
        elements.append(Spacer(1, 2*cm))

        # إضافة مكان للتوقيع
        signature_data = [
            [
                fix_arabic("توقيع ولي الأمر"),
                "",
                fix_arabic("توقيع الحراسة العامة")
            ]
        ]

        # إنشاء جدول التوقيعات
        signature_table = Table(signature_data, colWidths=[6*cm, 7*cm, 6*cm])

        # تنسيق جدول التوقيعات
        signature_table.setStyle(TableStyle([
            # بدون حدود
            ('GRID', (0, 0), (-1, -1), 0, colors.white),
            # الخط
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),
            # حجم الخط
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            # المحاذاة
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),  # توقيع ولي الأمر محاذاة لليسار
            ('ALIGN', (2, 0), (2, 0), 'RIGHT'),  # توقيع الحراسة العامة محاذاة لليمين
        ]))

        # إضافة جدول التوقيعات إلى المستند
        elements.append(signature_table)

        # إضافة تاريخ الطبع
        current_date = datetime.now().strftime("%Y-%m-%d")
        date_paragraph = Paragraph(fix_arabic(f"تاريخ الطبع: {current_date}"), arabic_style)
        elements.append(Spacer(1, 1*cm))
        elements.append(date_paragraph)

        # بناء المستند
        doc.build(elements)

        # فتح الملف مباشرة بدون عرض رسالة نجاح
        open_pdf(file_path)
        return True, file_path

    except Exception as e:
        print(f"خطأ في طباعة تبرير الغياب: {str(e)}")
        traceback.print_exc()
        QMessageBox.critical(
            None,
            "خطأ طباعة",
            f"حدث خطأ أثناء إنشاء أو طباعة ملف تبرير الغياب:\n{e}"
        )
        return False, None

# قسم اختباري
if __name__ == "__main__":
    # بيانات تجريبية للاختبار
    record_test = {
        'id': '22',
        'student_code': 'P134341017',
        'student_name': 'العدولي ايمن',
        'level': 'جذع مشترك علمي',
        'section': 'TCS-1',
        'justification_date': '2025-04-14',
        'start_date': '2025-04-14',
        'end_date': '2025-04-14',
        'days_count': '1',
        'reason': 'شهادة طبية',
        'notes': 'ملاحظات تجريبية حول التبرير.'
    }

    print("اختبار طباعة تبرير الغياب...")
    print_absence_justification(record_test)
