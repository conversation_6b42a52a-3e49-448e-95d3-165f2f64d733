import sys
import os
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QMessageBox, QDialog)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QDate, QObject, pyqtSlot
from PyQt5.QtSql import QSqlQuery
import sqlite3
import json
from urllib.parse import unquote

# تم إزالة استيراد sub100_window والاعتماد فقط على نظام الرسائل عبر الجسر

# استيراد دالة الطباعة المتطورة
try:
    from arabic_pdf_report import print_certificates_requests as arabic_print_function
    ARABIC_PDF_AVAILABLE = True
    print("تم استيراد دالة الطباعة من arabic_pdf_report.py بنجاح")
except ImportError:
    ARABIC_PDF_AVAILABLE = False
    print("تعذر استيراد arabic_pdf_report.py")

class WebBridge(QObject):
    """فئة الجسر بين JavaScript و Python"""
    
    # إشارات للتفاعل مع النافذة الرئيسية
    dataRequested = pyqtSignal()
    deliveryCertificates = pyqtSignal(list)
    deleteCertificates = pyqtSignal(list)
    printCertificates = pyqtSignal()
    toggleDeliveredRecords = pyqtSignal(bool)
    selectUndeliveredRecords = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        
    @pyqtSlot(str)
    def deliverSelectedCertificates(self, selected_codes):
        """استقبال قائمة الرموز المحددة لتسليم الشهادات"""
        try:
            codes_list = json.loads(selected_codes)
            if not codes_list:
                self.showMessage("الرجاء تحديد شهادة واحدة على الأقل لتسليمها", "warning")
                return
            self.deliveryCertificates.emit(codes_list)
        except Exception as e:
            print(f"خطأ في معالجة الرموز المحددة: {e}")
            self.showMessage("حدث خطأ في معالجة البيانات المحددة", "error")
    
    @pyqtSlot(str)
    def deleteSelectedCertificates(self, selected_codes):
        """استقبال قائمة الرموز المحددة لحذف السجلات"""
        try:
            codes_list = json.loads(selected_codes)
            if not codes_list:
                self.showMessage("الرجاء تحديد سجل واحد على الأقل لحذفه", "warning")
                return
            self.deleteCertificates.emit(codes_list)
        except Exception as e:
            print(f"خطأ في معالجة الرموز المحددة للحذف: {e}")
            self.showMessage("حدث خطأ في معالجة البيانات المحددة", "error")
    
    @pyqtSlot()
    def requestPrint(self):
        """طلب طباعة الشهادات"""
        try:
            self.printCertificates.emit()
        except Exception as e:
            print(f"خطأ في طلب الطباعة: {e}")
            self.showMessage("حدث خطأ أثناء محاولة الطباعة", "error")
    
    @pyqtSlot(bool)
    def toggleDelivered(self, show_delivered):
        """تبديل عرض السجلات المسلمة"""
        try:
            self.toggleDeliveredRecords.emit(show_delivered)
        except Exception as e:
            print(f"خطأ في تبديل عرض السجلات: {e}")
            self.showMessage("حدث خطأ أثناء تحديث العرض", "error")
    
    @pyqtSlot()
    def selectAllUndelivered(self):
        """تحديد جميع السجلات غير المسلمة"""
        try:
            self.selectUndeliveredRecords.emit()
        except Exception as e:
            print(f"خطأ في تحديد السجلات: {e}")
            self.showMessage("حدث خطأ أثناء تحديد السجلات", "error")
    
    @pyqtSlot()
    def refreshData(self):
        """طلب تحديث البيانات"""
        try:
            self.dataRequested.emit()
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            self.showMessage("حدث خطأ أثناء تحديث البيانات", "error")
    
    @pyqtSlot(str)
    def copyCodeToClipboard(self, code):
        """نسخ الرمز إلى الحافظة"""
        try:
            from PyQt5.QtGui import QClipboard
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(code)
            self.showMessage(f"تم نسخ الرمز: {code}", "success")
        except Exception as e:
            print(f"خطأ في نسخ الرمز: {e}")
            self.showMessage("حدث خطأ أثناء نسخ الرمز", "error")
    
    def showMessage(self, message, message_type="info"):
        """عرض رسالة للمستخدم عبر JavaScript فقط"""
        try:
            # إرسال الرسالة لـ JavaScript للعرض في الواجهة
            message_data = json.dumps({"message": message, "type": message_type}, ensure_ascii=False)
            if self.parent_window and hasattr(self.parent_window, 'web_view'):
                self.parent_window.web_view.page().runJavaScript(f"showNotification({message_data});")
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

class SchoolCertificateHtmlWindow(QMainWindow):
    """نافذة إدارة طلبات الشهادات المدرسية باستخدام HTML"""
    
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.db_path = os.path.join(os.path.dirname(__file__), "data.db")
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())
        self.show_delivered = False  # حالة عرض السجلات المسلمة
        
        self.setWindowTitle("إدارة طلبات الشهادات المدرسية")
        self.setMinimumSize(1200, 700)
        
        # إعداد أيقونة النافذة
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        self.setup_ui()
        self.setup_bridge()
        self.load_html_content()
        
        # إعداد خاص عند الدمج في النافذة الرئيسية
        if parent is not None:
            self.setup_for_embedding()
        
    def setup_for_embedding(self):
        """إعدادات خاصة عند دمج النافذة في النافذة الرئيسية"""
        try:
            # ضمان ظهور أسهم التمرير
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = self.web_view.settings()
            settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
            settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
            settings.setAttribute(QWebEngineSettings.SpatialNavigationEnabled, True)
            
            # إعدادات إضافية للنافذة
            self.setMinimumSize(1000, 700)
            
            print("INFO: تم تطبيق إعدادات الدمج لنافذة طلبات الشهادات المدرسية")
            
        except Exception as e:
            print(f"خطأ في إعداد النافذة للدمج: {e}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        
        # إعدادات خاصة للتمرير عند الدمج
        from PyQt5.QtWebEngineWidgets import QWebEngineSettings
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
        settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
        
        # إعدادات إضافية لضمان عمل التمرير
        self.web_view.setMinimumSize(800, 600)
        
        layout.addWidget(self.web_view)
        
    def setup_bridge(self):
        """إعداد الجسر بين Python و JavaScript"""
        self.bridge = WebBridge(self)
        
        # ربط الإشارات
        self.bridge.dataRequested.connect(self.load_data)
        self.bridge.deliveryCertificates.connect(self.deliver_certificates)
        self.bridge.deleteCertificates.connect(self.delete_certificates)
        self.bridge.printCertificates.connect(self.preview_and_print)
        self.bridge.toggleDeliveredRecords.connect(self.toggle_delivered_records)
        self.bridge.selectUndeliveredRecords.connect(self.select_undelivered_records)
        
    def load_html_content(self):
        """تحميل محتوى HTML"""
        html_content = self.generate_html()
        self.web_view.setHtml(html_content)
        
        # تحميل البيانات بعد تحميل HTML
        self.web_view.loadFinished.connect(self.on_load_finished)
        
    def on_load_finished(self):
        """يتم استدعاؤها عند انتهاء تحميل الصفحة"""
        # إعداد القناة للتواصل مع JavaScript
        from PyQt5.QtWebChannel import QWebChannel
        
        self.channel = QWebChannel()
        self.channel.registerObject("bridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)
        
        # تأخير قصير للتأكد من تحميل QWebChannel
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(500, self.load_data)
        
    def generate_html(self):
        """إنشاء محتوى HTML للنافذة"""
        return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الشهادات المدرسية</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            /* إعدادات التمرير للجسم */
            overflow: auto;
            scrollbar-width: auto;
            -ms-overflow-style: auto;
        }
        
        /* تخصيص أسهم التمرير للجسم */
        body::-webkit-scrollbar {
            width: 14px;
        }
        
        body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            /* إعدادات التمرير للحاوية */
            overflow: visible;
            min-height: 600px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        
        .header h1 {
            color: #1e3a8a;
            font-family: 'Calibri', sans-serif;
            font-size: 22pt;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: nowrap;
            justify-content: center;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(255, 255, 255, 0.95);
            padding: 10px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            cursor: pointer;
            transition: none;
            min-width: 200px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            white-space: nowrap;
        }
        
        .btn:hover {
            /* إزالة تأثيرات الحركة للجعل الأزرار ثابتة */
            transform: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn-purple {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }
        
        .btn-toggle {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .btn-toggle.active {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 17px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            font-size: 16px;
            font-weight: bold;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
            transform: scale(1.2);
        }
        
        .code-cell {
            color: #3498db;
            font-weight: bold;
            cursor: pointer;
        }
        
        .code-cell:hover {
            color: #2980b9;
            text-decoration: underline;
        }
        
        .delivered {
            background-color: #d5f4e6 !important;
        }
        
        .status-delivered {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #7f8c8d;
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #95a5a6;
        }
        
        .selected-count {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            margin-right: 15px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .btn {
                min-width: 100%;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
                font-size: 12px;
            }
        }
        
        /* تحسينات إضافية للجدول */
        .table-wrapper {
            max-height: 60vh;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 10px;
            /* إجبار ظهور أسهم التمرير */
            scrollbar-width: auto; /* Firefox */
            -ms-overflow-style: auto; /* IE and Edge */
        }
        
        .table-wrapper::-webkit-scrollbar {
            width: 14px;
            height: 14px;
            background: #f8f9fa;
        }
        
        .table-wrapper::-webkit-scrollbar-track {
            background: #e9ecef;
            border-radius: 7px;
            border: 1px solid #dee2e6;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 7px;
            border: 1px solid #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-corner {
            background: #e9ecef;
        }
        
        /* إجبار ظهور التمرير دائماً */
        .table-wrapper {
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #6c757d #e9ecef; /* Firefox */
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 إدارة طلبات الشهادات المدرسية</h1>
            <p>نظام متطور لإدارة وتتبع طلبات الشهادات المدرسية</p>
            <span id="selectedCount" class="selected-count" style="display: none; margin-top: 15px;">تم تحديد 0 عنصر</span>
        </div>
        
        <div class="controls">
            
            <button id="toggleBtn" class="btn btn-toggle" onclick="toggleDeliveredRecords()">
                عرض السجلات المسلمة
            </button>
            
            <button id="selectUndeliveredBtn" class="btn btn-warning" onclick="selectUndeliveredRecords()">
                تحديد السجلات غير المسلمة
            </button>
            
            <button id="deliverBtn" class="btn btn-success" onclick="deliverCertificates()">
                تسليم الشهادات المحددة
            </button>
            
            <button id="deleteBtn" class="btn btn-danger" onclick="deleteCertificates()">
                حذف السجلات المحددة
            </button>
            
            <button id="printBtn" class="btn btn-primary" onclick="printCertificates()">
                معاينة وطباعة التقرير
            </button>
            
            <button class="btn btn-purple" onclick="refreshData()">
                تحديث البيانات
            </button>
        </div>
        
        <div class="table-container">
            <div class="table-wrapper">
                <table class="table" id="certificatesTable">
                    <thead>
                        <tr>
                            <th style="width: 60px;">
                                <input type="checkbox" id="selectAll" class="checkbox" onchange="toggleSelectAll()">
                            </th>
                            <th style="width: 100px;">الرمز</th>
                            <th style="width: 80px;">السنة</th>
                            <th style="width: 80px;">الرقم</th>
                            <th style="width: 120px;">القسم</th>
                            <th style="width: 250px;">الاسم والنسب</th>
                            <th style="width: 120px;">تاريخ الطلب</th>
                            <th style="width: 120px;">تاريخ التسليم</th>
                            <th style="width: 150px;">ملاحظات</th>
                            <th style="width: 120px;">مكتب الحراسة</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="10" class="loading">جاري تحميل البيانات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let bridge;
        let showDelivered = false;
        let certificatesData = [];
        let selectedCertificates = new Set();
        
        // إعداد القناة مع Python مع معالجة للأخطاء
        function setupBridge() {
            try {
                if (typeof QWebChannel !== 'undefined') {
                    // التحقق من وجود qt.webChannelTransport
                    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                        new QWebChannel(qt.webChannelTransport, function(channel) {
                            bridge = channel.objects.bridge;
                            console.log('تم إنشاء الجسر بنجاح');
                        });
                    } else {
                        console.log('qt.webChannelTransport غير متوفر، إعادة المحاولة...');
                        setTimeout(setupBridge, 200);
                    }
                } else {
                    console.log('QWebChannel غير متوفر، إعادة المحاولة...');
                    setTimeout(setupBridge, 200);
                }
            } catch (error) {
                console.log('خطأ في إعداد الجسر:', error);
                setTimeout(setupBridge, 200);
            }
        }
        
        // بدء إعداد الجسر عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupBridge();
        });
        
        // محاولة إضافية بعد تحميل النافذة
        window.addEventListener('load', function() {
            if (!bridge) {
                setTimeout(setupBridge, 300);
            }
        });
        
        function updateTable(data) {
            certificatesData = data;
            const tableBody = document.getElementById('tableBody');
            
            if (!data || data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="10" class="no-data">لا توجد بيانات للعرض</td></tr>';
                return;
            }
            
            let html = '';
            data.forEach((row, index) => {
                const isDelivered = row[7] && row[7] !== '';
                const rowClass = isDelivered ? 'delivered' : '';
                const statusClass = isDelivered ? 'status-delivered' : 'status-pending';
                const statusText = isDelivered ? 'تم التسليم' : 'لم يتم التسليم';
                
                html += `
                    <tr class="${rowClass}">
                        <td>
                            <input type="checkbox" class="checkbox" data-code="${row[0]}" 
                                   onchange="toggleCertificate('${row[0]}', this.checked)">
                        </td>
                        <td class="code-cell" onclick="copyCode('${row[0]}')">${row[0] || ''}</td>
                        <td>${row[1] || ''}</td>
                        <td>${row[2] || ''}</td>
                        <td>${row[3] || ''}</td>
                        <td style="text-align: right; font-weight: bold;">${row[4] || ''}</td>
                        <td>${row[5] || ''}</td>
                        <td class="${statusClass}">${row[6] || statusText}</td>
                        <td>${row[7] || ''}</td>
                        <td>${row[8] || ''}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
            updateSelectedCount();
            
            // إعادة تطبيق إعدادات التمرير بعد تحديث البيانات
            setTimeout(function() {
                const wrapper = document.querySelector('.table-wrapper');
                if (wrapper) {
                    wrapper.style.overflowY = 'auto';
                    wrapper.style.overflowX = 'auto';
                    wrapper.style.scrollbarWidth = 'auto';
                    wrapper.style.msOverflowStyle = 'auto';
                }
            }, 100);
        }
        
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('tbody .checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                const code = checkbox.getAttribute('data-code');
                if (selectAllCheckbox.checked) {
                    selectedCertificates.add(code);
                } else {
                    selectedCertificates.delete(code);
                }
            });
            
            updateSelectedCount();
        }
        
        function toggleCertificate(code, isChecked) {
            if (isChecked) {
                selectedCertificates.add(code);
            } else {
                selectedCertificates.delete(code);
            }
            updateSelectedCount();
        }
        
        function updateSelectedCount() {
            const count = selectedCertificates.size;
            const countElement = document.getElementById('selectedCount');
            
            if (count > 0) {
                countElement.textContent = `تم تحديد ${count} عنصر`;
                countElement.style.display = 'inline-block';
            } else {
                countElement.style.display = 'none';
            }
        }
        
        function copyCode(code) {
            if (bridge && typeof bridge.copyCodeToClipboard === 'function') {
                bridge.copyCodeToClipboard(code);
            } else {
                // Fallback للنسخ المباشر إذا كان الجسر غير متاح
                navigator.clipboard.writeText(code).then(() => {
                    console.log('تم نسخ الرمز:', code);
                }).catch(() => {
                    console.log('فشل في نسخ الرمز:', code);
                });
            }
        }
        
        function toggleDeliveredRecords() {
            showDelivered = !showDelivered;
            const toggleBtn = document.getElementById('toggleBtn');
            
            if (showDelivered) {
                toggleBtn.textContent = 'إخفاء السجلات المسلمة';
                toggleBtn.classList.add('active');
            } else {
                toggleBtn.textContent = 'عرض السجلات المسلمة';
                toggleBtn.classList.remove('active');
            }
            
            if (bridge && typeof bridge.toggleDelivered === 'function') {
                bridge.toggleDelivered(showDelivered);
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
            }
        }
        
        function selectUndeliveredRecords() {
            // مسح التحديدات السابقة
            selectedCertificates.clear();
            
            // تحديد جميع السجلات غير المسلمة
            const checkboxes = document.querySelectorAll('tbody .checkbox');
            checkboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const isDelivered = row.classList.contains('delivered');
                
                if (!isDelivered) {
                    checkbox.checked = true;
                    const code = checkbox.getAttribute('data-code');
                    selectedCertificates.add(code);
                }
            });
            
            updateSelectedCount();
            
            if (bridge && typeof bridge.selectAllUndelivered === 'function') {
                bridge.selectAllUndelivered();
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
            }
        }
        
        function deliverCertificates() {
            if (bridge && typeof bridge.deliverSelectedCertificates === 'function') {
                const selectedArray = Array.from(selectedCertificates);
                bridge.deliverSelectedCertificates(JSON.stringify(selectedArray));
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                // عرض رسالة عبر نظام الإشعارات الداخلي
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
            }
        }
        
        function deleteCertificates() {
            if (selectedCertificates.size === 0) {
                showNotification({message: 'الرجاء تحديد سجل واحد على الأقل لحذفه', type: 'warning'});
                return;
            }
            
            if (bridge && typeof bridge.deleteSelectedCertificates === 'function') {
                const selectedArray = Array.from(selectedCertificates);
                bridge.deleteSelectedCertificates(JSON.stringify(selectedArray));
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
            }
        }
        
        function printCertificates() {
            if (bridge && typeof bridge.requestPrint === 'function') {
                bridge.requestPrint();
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
            }
        }
        
        function refreshData() {
            document.getElementById('tableBody').innerHTML = 
                '<tr><td colspan="10" class="loading">جاري تحديث البيانات...</td></tr>';
            
            if (bridge && typeof bridge.refreshData === 'function') {
                bridge.refreshData();
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
            }
        }
        
        function clearSelections() {
            selectedCertificates.clear();
            const checkboxes = document.querySelectorAll('.checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        }
        
        // دالة لعرض الإشعارات الداخلية بدلاً من alert
        function showNotification(notificationData) {
            try {
                // إنشاء عنصر الإشعار
                const notification = document.createElement('div');
                notification.className = `notification notification-${notificationData.type || 'info'}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <span class="notification-message">${notificationData.message}</span>
                        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                    </div>
                `;
                
                // إضافة الأنماط إذا لم تكن موجودة
                if (!document.getElementById('notification-styles')) {
                    const styles = document.createElement('style');
                    styles.id = 'notification-styles';
                    styles.textContent = `
                        .notification {
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            z-index: 10000;
                            padding: 15px 20px;
                            border-radius: 8px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            max-width: 400px;
                            direction: rtl;
                            text-align: right;
                            animation: slideIn 0.3s ease-out;
                        }
                        .notification-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
                        .notification-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
                        .notification-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
                        .notification-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
                        .notification-content { display: flex; align-items: center; justify-content: space-between; }
                        .notification-message { flex: 1; margin-right: 10px; }
                        .notification-close {
                            background: none; border: none; font-size: 18px; cursor: pointer;
                            color: inherit; opacity: 0.7; padding: 0; width: 20px; height: 20px;
                        }
                        .notification-close:hover { opacity: 1; }
                        @keyframes slideIn {
                            from { transform: translateX(100%); opacity: 0; }
                            to { transform: translateX(0); opacity: 1; }
                        }
                    `;
                    document.head.appendChild(styles);
                }
                
                // إضافة الإشعار للصفحة
                document.body.appendChild(notification);
                
                // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
                
            } catch (error) {
                console.error('خطأ في عرض الإشعار:', error);
                // fallback لـ console في حالة فشل عرض الإشعار
                console.log(`إشعار [${notificationData.type}]: ${notificationData.message}`);
            }
        }
        
        // دالة لإعادة تطبيق إعدادات التمرير
        function reapplyScrollbarSettings() {
            try {
                const tableWrapper = document.querySelector('.table-wrapper');
                if (tableWrapper) {
                    tableWrapper.style.overflowY = 'auto';
                    tableWrapper.style.overflowX = 'auto';
                    tableWrapper.style.scrollbarWidth = 'auto';
                    tableWrapper.style.msOverflowStyle = 'auto';
                }
                
                // للجسم أيضاً
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                console.log('تم إعادة تطبيق إعدادات التمرير لنافذة الشهادات');
            } catch (error) {
                console.error('خطأ في إعادة تطبيق إعدادات التمرير:', error);
            }
        }
        
        // تطبيق إعدادات التمرير عند تحميل النافذة
        window.addEventListener('load', function() {
            setTimeout(reapplyScrollbarSettings, 500);
            setTimeout(reapplyScrollbarSettings, 1500);
        });
    </script>
</body>
</html>
        '''
        
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات وإرسالها إلى HTML"""
        try:
            # تعديل الاستعلام ليأخذ في الاعتبار حالة عرض السجلات المسلمة
            query_str = """
                SELECT الرمز, السنة, الرقم, القسم, الاسم_والنسب,
                       تاريخ_الطلب, تاريخ_التسليم, ملاحظات, مكتب_الحراسة
                FROM الشهادة_المدرسية
                WHERE تاريخ_التسليم IS {}
                ORDER BY الرقم DESC
            """.format("NOT NULL" if self.show_delivered else "NULL")

            if self.using_qsql:
                query = QSqlQuery(self.db)
                if not query.exec_(query_str):
                    raise Exception(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")

                records = []
                while query.next():
                    record = []
                    for i in range(9):  # 9 أعمدة
                        value = query.value(i)
                        record.append(str(value) if value is not None else '')
                    records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                records = cursor.fetchall()
                conn.close()
                
                # تحويل None إلى نص فارغ
                records = [[str(value) if value is not None else '' for value in record] for record in records]

            # إرسال البيانات إلى JavaScript
            records_json = json.dumps(records, ensure_ascii=False)
            self.web_view.page().runJavaScript(f"updateTable({records_json});")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.bridge.showMessage(
                f"حدث خطأ أثناء تحميل البيانات: {str(e)}",
                "error"
            )
            
    def deliver_certificates(self, selected_codes):
        """تسليم الشهادات المحددة"""
        try:
            if not selected_codes:
                self.bridge.showMessage(
                    "الرجاء تحديد شهادة واحدة على الأقل لتسليمها",
                    "warning"
                )
                return

            # تأكد من أن selected_codes هو قائمة صحيحة
            if isinstance(selected_codes, str):
                try:
                    selected_codes = json.loads(selected_codes)
                except:
                    selected_codes = [selected_codes]
            elif not isinstance(selected_codes, list):
                selected_codes = list(selected_codes) if selected_codes else []

            print(f"أكواد الشهادات المحددة: {selected_codes}")

            # تنفيذ عملية التسليم مباشرة بدون تأكيد
            self.process_delivery_confirmation(selected_codes)
                
        except Exception as e:
            print(f"خطأ في تسليم الشهادات: {e}")
            import traceback
            traceback.print_exc()
            self.bridge.showMessage(f"حدث خطأ في تسليم الشهادات: {str(e)}", "error")
    
    def process_delivery_confirmation(self, selected_codes):
        """معالجة تأكيد التسليم بعد موافقة المستخدم"""
        try:
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            success_count = 0
            error_messages = []

            for code in selected_codes:
                try:
                    if self.using_qsql:
                        query = QSqlQuery(self.db)
                        query.prepare("""
                            UPDATE الشهادة_المدرسية
                            SET اختيار = 1,
                                تاريخ_التسليم = ?,
                                ملاحظات = 'تم التسليم'
                            WHERE الرمز = ?
                        """)
                        query.addBindValue(current_date)
                        query.addBindValue(code)
                        if not query.exec_():
                            error_messages.append(f"خطأ في تحديث السجل رقم {code}: {query.lastError().text()}")
                            continue
                    else:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE الشهادة_المدرسية
                            SET اختيار = 1,
                                تاريخ_التسليم = ?,
                                ملاحظات = 'تم التسليم'
                            WHERE الرمز = ?
                        """, (current_date, code))
                        conn.commit()
                        conn.close()

                    success_count += 1
                except Exception as e:
                    error_messages.append(f"خطأ في معالجة الرمز {code}: {str(e)}")

            # عرض رسالة النتيجة عبر الجسر
            if success_count > 0:
                success_message = f"تم تسليم {success_count} شهادة مدرسية بنجاح"
                if error_messages:
                    success_message += f" مع وجود {len(error_messages)} أخطاء"

                self.bridge.showMessage(success_message, "success")

                # تحديث البيانات ومسح التحديدات
                self.load_data()
                self.web_view.page().runJavaScript("clearSelections();")

            elif error_messages:
                error_details = " | ".join(error_messages[:3])
                if len(error_messages) > 3:
                    error_details += f" وأخطاء أخرى ({len(error_messages)})"

                self.bridge.showMessage(
                    f"لم يتم تسليم أي شهادة. الأخطاء: {error_details}",
                    "error"
                )

        except Exception as e:
            self.bridge.showMessage(f"حدث خطأ أثناء تسليم الشهادات: {str(e)}", "error")
    
    def preview_and_print(self):
        """معاينة وطباعة التقرير"""
        try:
            # إنشاء المجلد الرئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)

            # إنشاء المجلد الفرعي لتقارير طلبات الشواهد المدرسية
            reports_dir = os.path.join(main_folder, "تقارير طلبات الشواهد المدرسية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # عرض رسالة انتظار
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # استخدام دالة الطباعة المتطورة
            if ARABIC_PDF_AVAILABLE:
                result, filepath, certificates_dir = arabic_print_function(self)
            else:
                self.bridge.showMessage(
                    "ملف الطباعة المتطور arabic_pdf_report.py غير متوفر",
                    "error"
                )
                return

            # إعادة المؤشر إلى الوضع الطبيعي
            QApplication.restoreOverrideCursor()

            if result and os.path.exists(filepath):
                # إرسال رسالة نجاح وفتح الملف مباشرة
                self.bridge.showMessage(
                    f"تم إنشاء التقرير بنجاح وحفظه في المسار: {filepath}",
                    "success"
                )
                
                # فتح الملف مباشرة
                try:
                    os.startfile(filepath)
                except Exception as e:
                    self.bridge.showMessage(f"حدث خطأ أثناء فتح الملف: {str(e)}", "error")
            else:
                if not filepath and not certificates_dir:
                    pass  # إلغاء من المستخدم
                elif not os.path.exists(filepath):
                    self.bridge.showMessage(
                        "حدث خطأ أثناء طباعة التقرير - لم يتم إنشاء الملف بشكل صحيح",
                        "warning"
                    )

        except Exception as e:
            self.bridge.showMessage(f"حدث خطأ أثناء طباعة التقرير: {str(e)}", "error")
    
    def delete_certificates(self, selected_codes):
        """حذف السجلات المحددة"""
        try:
            if not selected_codes:
                self.bridge.showMessage(
                    "الرجاء تحديد سجل واحد على الأقل لحذفه",
                    "warning"
                )
                return

            # تأكد من أن selected_codes هو قائمة صحيحة
            if isinstance(selected_codes, str):
                try:
                    selected_codes = json.loads(selected_codes)
                except:
                    selected_codes = [selected_codes]
            elif not isinstance(selected_codes, list):
                selected_codes = list(selected_codes) if selected_codes else []

            print(f"أكواد السجلات المحددة للحذف: {selected_codes}")

            # تنفيذ عملية الحذف مباشرة بدون تأكيد
            self.process_delete_confirmation(selected_codes)
                
        except Exception as e:
            print(f"خطأ في حذف السجلات: {e}")
            import traceback
            traceback.print_exc()
            self.bridge.showMessage(f"حدث خطأ في حذف السجلات: {str(e)}", "error")
    
    def process_delete_confirmation(self, selected_codes):
        """معالجة حذف السجلات المحددة"""
        try:
            success_count = 0
            error_messages = []

            for code in selected_codes:
                try:
                    if self.using_qsql:
                        query = QSqlQuery(self.db)
                        query.prepare("""
                            DELETE FROM الشهادة_المدرسية
                            WHERE الرمز = ?
                        """)
                        query.addBindValue(code)
                        if not query.exec_():
                            error_messages.append(f"خطأ في حذف السجل رقم {code}: {query.lastError().text()}")
                            continue
                    else:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        cursor.execute("""
                            DELETE FROM الشهادة_المدرسية
                            WHERE الرمز = ?
                        """, (code,))
                        conn.commit()
                        conn.close()

                    success_count += 1
                except Exception as e:
                    error_messages.append(f"خطأ في حذف الرمز {code}: {str(e)}")

            # عرض رسالة النتيجة عبر الجسر
            if success_count > 0:
                success_message = f"تم حذف {success_count} سجل بنجاح"
                if error_messages:
                    success_message += f" مع وجود {len(error_messages)} أخطاء"

                self.bridge.showMessage(success_message, "success")

                # تحديث البيانات ومسح التحديدات
                self.load_data()
                self.web_view.page().runJavaScript("clearSelections();")

            elif error_messages:
                error_details = " | ".join(error_messages[:3])
                if len(error_messages) > 3:
                    error_details += f" وأخطاء أخرى ({len(error_messages)})"

                self.bridge.showMessage(
                    f"لم يتم حذف أي سجل. الأخطاء: {error_details}",
                    "error"
                )

        except Exception as e:
            self.bridge.showMessage(f"حدث خطأ أثناء حذف السجلات: {str(e)}", "error")
    
    def toggle_delivered_records(self, show_delivered):
        """تبديل عرض السجلات المسلمة"""
        self.show_delivered = show_delivered
        self.load_data()
        
    def select_undelivered_records(self):
        """تحديد جميع السجلات غير المسلمة"""
        try:
            # التأكد من أن النافذة تعرض السجلات غير المسلمة
            if self.show_delivered:
                self.show_delivered = False
                self.load_data()

            # إشعار المستخدم بالنجاح
            self.bridge.showMessage("تم تحديد جميع السجلات غير المسلمة بنجاح", "success")

        except Exception as e:
            self.bridge.showMessage(f"حدث خطأ في تحديد السجلات: {str(e)}", "error")

    def ensure_maximized(self):
        """التأكد من عرض النافذة في كامل الشاشة"""
        self.showMaximized()
        QApplication.processEvents()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم داكن اختياري
    app.setStyle('Fusion')
    
    window = SchoolCertificateHtmlWindow()
    window.show()
    sys.exit(app.exec_())
